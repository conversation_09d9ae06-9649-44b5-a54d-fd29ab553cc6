#!/usr/bin/env python3
"""
Test to verify the PipecatWidget fix is working.
"""

import requests
import sys

API_BASE_URL = "http://localhost:7860"

def main():
    print("🔧 PipecatWidget Fix Verification")
    print("=" * 50)
    
    # Check server
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Server not running")
            return False
    except:
        print("❌ Server not accessible")
        return False
    
    print("✅ Server is running")
    
    # Get test agent
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            active_agents = [a for a in agents if a.get('status') == 'active']
            
            if active_agents:
                agent = active_agents[0]
                agent_id = agent['id']
                agent_name = agent['name']
                print(f"✅ Found test agent: {agent_name}")
                
                print(f"\n🔧 Key Fix Applied:")
                print(f"   • Removed problematic cleanup effect that depended on transportState")
                print(f"   • Added proper cleanup only on component unmount (like SimpleVoiceWidget)")
                print(f"   • Fixed premature disconnection issue")
                
                print(f"\n🧪 Testing Instructions:")
                print(f"1. Open http://localhost:3000/test")
                print(f"2. Select agent: {agent_name}")
                print(f"3. Test PipecatWidget (Original):")
                print(f"   • Should now connect successfully")
                print(f"   • Should NOT disconnect immediately")
                print(f"   • Should maintain stable connection")
                print(f"   • Should play bot audio responses")
                
                print(f"\n📊 Expected Behavior:")
                print(f"   ✅ Connection establishes successfully")
                print(f"   ✅ No immediate disconnection")
                print(f"   ✅ Bot audio plays correctly")
                print(f"   ✅ Connection remains stable during conversation")
                
                print(f"\n🔍 Server Logs Should Show:")
                print(f"   ✅ Pipeline starts and stays running")
                print(f"   ✅ No 'Stopping pipeline runner...' messages")
                print(f"   ✅ No 'tasks cancelled error' messages")
                print(f"   ✅ TTS processing and bot speaking events")
                
                print(f"\n🎯 The Fix:")
                print(f"   • Removed: useEffect cleanup that ran on transportState changes")
                print(f"   • Added: Proper cleanup only on component unmount/client change")
                print(f"   • Result: No more premature disconnections!")
                
                return True
            else:
                print("❌ No active agents found")
                print("   Run: python create_test_agent.py")
                return False
        else:
            print(f"❌ Failed to get agents: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
