# bot_framework.py (Complete, with Subprocess Management - Final, Corrected Version)
import asyncio
import time
from loguru import logger
from datetime import datetime, timezone
from typing import Optional, List
from utils.call_log_service import CallLogService
from utils.call_log_model import CallLogEntry
import uuid
import signal
from abc import ABC, abstractmethod
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.pipeline.runner import PipelineRunner
from pipecat.processors.frameworks.rtvi import RTVIConfig, RTVIProcessor
from pipecat.services.deepgram import DeepgramSTTService
from deepgram import LiveTranscriptionEvents, LiveResultResponse
from .services import ServiceRegistry
from .transports import TransportFactory
from .events import EventFramework
from .pipelines import PipelineBuilder
from pipecat.audio.vad.vad_analyzer import VADParams
import contextvars  # Import contextvars

# --- Subprocess Management ---
class SubprocessManager:
    def __init__(self):
        self.subprocesses: List[asyncio.subprocess.Process] = []

    def add_subprocess(self, process: asyncio.subprocess.Process):
        self.subprocesses.append(process)

    async def terminate_all(self):
        for process in self.subprocesses:
            if process.returncode is None:  # Check if still running
                try:
                    logger.info(f"Terminating subprocess (PID: {process.pid})...")
                    process.terminate()
                    await asyncio.wait_for(process.wait(), timeout=5)  # Give it time
                except TimeoutError:
                    logger.warning(f"Subprocess (PID: {process.pid}) did not terminate gracefully, killing...")
                    process.kill()
                    await process.wait()
                except ProcessLookupError:
                    logger.warning(f"Subprocess (PID: {process.pid}) already exited")
                except Exception as e:
                    logger.error(f"Error terminating subprocess (PID: {process.pid}): {e}")

# --- Monkey-patch asyncio to track subprocess creation ---
_original_create_subprocess_exec = asyncio.create_subprocess_exec
# Context variable to store the bot instance.
bot_instance_contextvar = contextvars.ContextVar('bot_instance', default=None)

async def _patched_create_subprocess_exec(*args, **kwargs):
    process = await _original_create_subprocess_exec(*args, **kwargs)
    # Get the current bot instance from the context variable.
    bot = bot_instance_contextvar.get()
    if bot:
        bot.subprocess_manager.add_subprocess(process)
    else:
        logger.warning("Subprocess created outside of a BaseBot context. It won't be tracked!")
    return process

asyncio.create_subprocess_exec = _patched_create_subprocess_exec
# --- End Monkey-patch ---

class BaseBot(ABC):
    def __init__(self, config, agent_config=None, stt_service=None, stt_config=None, llm_service=None, llm_config=None, tts_service=None, tts_config=None, vad_config=None, call_log_service=None, room_url=None, app=None): # Add app
        super().__init__()
        self.config = config
        self.agent_config = agent_config
        self.stt_service = stt_service
        self.stt_config = stt_config
        self.llm_service = llm_service
        self.llm_config = llm_config
        self.tts_service = tts_service
        self.tts_config = tts_config
        self.services = ServiceRegistry(config)  # Initialize ServiceRegistry
        self.runner = None
        self.transport = None
        self.task = None
        self.rtvi = None
        self.context = None
        self.context_aggregator = None
        self.pipeline_builder = None  # Initialize pipeline_builder
        self.vad_config = vad_config
        self.last_message_time = time.time()
        self.call_log_service: Optional[CallLogService] = call_log_service
        self.call_log_id: Optional[str] = None
        self.transcript: str = ""
        self.room_url = room_url # Store room_url
        self._cleaned_up = False  # Add a flag to track cleanup status
        self.app = app # Store a reference to the FastAPI app
        self.shutdown_event = asyncio.Event() # Create an asyncio Event
        self.subprocess_manager = SubprocessManager() # Create a SubprocessManager

        # Set the bot instance in the context variable.
        bot_instance_contextvar.set(self)


    async def setup_services(self):
        """Initialize required services."""
        rtvi_config = RTVIConfig(config=[])
        self.rtvi = RTVIProcessor(config=rtvi_config)

        @self.rtvi.event_handler("on_client_ready")
        async def on_client_ready(rtvi):
            await rtvi.set_bot_ready()
            logger.info("RTVI client ready event handled in BaseBot.")

        self.services.initialize_services(self.config, self.agent_config)
        await self._setup_services_impl()

        if isinstance(self.services.stt, DeepgramSTTService):
            stt_service = self.services.stt
            connection = stt_service._connection
            if connection:
                connection.on(LiveTranscriptionEvents.Transcript, self._on_deepgram_transcript)
                logger.info("Registered event handler for Deepgram LiveTranscriptionEvents.Transcript")
            else:
                logger.warning("DeepgramSTTService connection object not found, cannot register transcript event handler.")
        else:
            logger.warning("STT Service is not DeepgramSTTService, skipping Deepgram transcript event handler registration.")

    @abstractmethod
    async def _setup_services_impl(self):
        """Implementation-specific service setup."""
        pass

    @abstractmethod
    async def _create_transport(self, factory: TransportFactory, url: str, token: str, vad_config: VADParams = None):
        """Implementation-specific transport creation."""
        pass

    @abstractmethod
    async def _handle_first_participant_impl(self):
        """Implementation-specific first participant handling."""
        pass

    async def _handle_first_participant(self):
        """Wrapper for first participant handling."""
        if self.call_log_service:
            # Get the current time and format it correctly for PostgreSQL.
            now = datetime.now(timezone.utc)
            start_time = now.strftime("%Y-%m-%dT%H:%M:%S%z")  # Correct format

            call_log_entry = CallLogEntry(
                agent_id=self.agent_config.get("id") if self.agent_config else None,
                room_url=self.room_url,  # Use self.room_url
                start_time=start_time  # Use the correctly formatted timestamp
            )
            created_log_entry = await self.call_log_service.create_call_log_entry(call_log_entry)
            self.call_log_id = created_log_entry.id
            logger.info(f"Call log entry created in Supabase with ID: {self.call_log_id}")
        else:
            logger.warning("CallLogService is not initialized, call logging will be skipped.")
        await self._handle_first_participant_impl()

    async def setup_transport(self, url: str, token: str):
        """Transport setup with event handlers."""
        transport_factory = TransportFactory(self.config)
        self.transport = await self._create_transport(transport_factory, url, token, self.vad_config)  # This is now the *wrapped* transport

        event_framework = EventFramework(self.transport)
        await event_framework.register_default_handlers(self.cleanup)

        @self.transport.event_handler("on_participant_left")
        async def on_participant_left(transport, participant, reason):
            logger.info(f"Participant left: {participant['id']}")
            # await self.cleanup()  <-- REMOVE THIS LINE



        @self.transport.event_handler("on_first_participant_joined")
        async def on_first_participant_joined(transport, participant):
            self.last_message_time = time.time()
            await transport.capture_participant_transcription(participant["id"])  # Use transport
            await self._handle_first_participant() #The timestamp is created here
            logger.info(f"First participant {participant['id']} joined, transcription started.")

        @self.rtvi.event_handler("on_client_ready")
        async def on_client_ready_rtvi(rtvi):
            await rtvi.set_bot_ready()
            logger.info("RTVI client ready event handled in setup_transport.")

        await self._setup_transport_impl()

    async def _setup_transport_impl(self):
        """Optional implementation-specific transport setup."""
        pass

    def create_pipeline(self):
        """Pipeline construction, now using the wrapped transport."""
        # Create the PipelineBuilder *after* the transport is wrapped
        self.pipeline_builder = PipelineBuilder(
            self.transport,  # Pass the *wrapped* transport
            self.services.stt,
            self.services.tts,
            self.services.llm,
            context=self.context,
        )
        pipeline = self.pipeline_builder.add_rtvi(self.rtvi).build()

        self.task = PipelineTask(
            pipeline,
            PipelineParams(
                allow_interruptions=True,
                enable_metrics=True,
                enable_usage_metrics=True,
                observers=[self.rtvi.observer()],
            ),
        )
        self.runner = PipelineRunner() # NO SubprocessManager here!
        asyncio.create_task(self._create_pipeline_impl())  # Run subclass-specific pipeline setup

    @abstractmethod
    async def _create_pipeline_impl(self):
        """Implementation-specific pipeline setup (subclasses override)."""
        self.services.llm.set_callback(self.update_last_message_time)
        self.services.stt.set_callback(self.update_last_message_time)
        pass

    async def start(self):
        """Start the bot."""
        if self.runner and self.task:
            logger.info("Starting pipeline runner...")

            # --- Signal Handling ---
            def signal_handler(sig, frame):
                logger.info("Signal received, initiating graceful shutdown...")
                asyncio.create_task(self.stop())  # Call self.stop() as a task

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            # --- End Signal Handling ---

            try:
                await self.runner.run(self.task)
            except asyncio.CancelledError:
                logger.info("Pipeline runner cancelled.")
                await self.cleanup()
                raise

            # Wait for shutdown_event to be set (by self.stop())
            if self.shutdown_event.is_set():
                await self.cleanup()

            logger.info("Pipeline runner finished.")
        else:
            logger.error("Bot not properly initialized.  Call setup methods first.")
            raise RuntimeError("Bot not properly initialized.")

    async def stop(self):
        """Gracefully stop the bot."""
        if self.runner:
            logger.info("Stopping pipeline runner - cancellation...")
            await self.runner.cancel()
        self.shutdown_event.set()  # Signal any waiting tasks to stop
        # await self.cleanup() REMOVE THIS. Cleanup will be called in start()


    async def cleanup(self):
        """Clean up resources.  Made idempotent."""
        if self._cleaned_up:
            logger.info("Cleanup already called. Skipping.")
            return

        logger.info("Starting cleanup process and call logging...")
        # Get the current time and format it for PostgreSQL.
        now = datetime.now(timezone.utc)
        end_time = now.strftime("%Y-%m-%dT%H:%M:%S%z")  # Correct format.

        call_duration = None
        if self.call_log_id and self.call_log_service:
            start_log_entry = await self.call_log_service.get_call_log_entry(self.call_log_id)
            if start_log_entry and start_log_entry.start_time:
                # Parse the start_time, ensuring it's timezone-aware.
                start_datetime = datetime.fromisoformat(start_log_entry.start_time.replace('Z', '+00:00'))
                # Use datetime.now(timezone.utc) directly
                end_datetime = now

                call_duration = int((end_datetime - start_datetime).total_seconds())

            updates = {
                "end_time": end_time,
                "call_duration_seconds": call_duration,
                "full_transcript": self.transcript,
                "status": "completed"
            }
            updated_log_entry = await self.call_log_service.update_call_log_entry(self.call_log_id, updates)
            if updated_log_entry:
                logger.info(
                    f"Call log entry updated in Supabase with ID: {self.call_log_id}, duration: {call_duration} seconds, status: completed.")
                logger.debug(f"Full transcript logged for call ID: {self.call_log_id}:\n{self.transcript}")
            else:
                logger.warning(f"Failed to update call log entry with ID: {self.call_log_id} in Supabase.")
        else:
            logger.warning("CallLogService not initialized or no call_log_id, skipping call log update.")
        # --- Terminate Subprocesses BEFORE leaving the room ---
        await self.subprocess_manager.terminate_all()

        if self.runner:
            logger.info("Stopping pipeline runner...")
            await self.runner.stop_when_done()  # This should already be handled
        if self.transport:
            logger.info("Leaving Daily room...")
            await self.transport.leave()

        # Room deletion moved to server.py's room_cleanup_task

        self._cleaned_up = True
        logger.info("Cleanup process completed.")




    async def _on_deepgram_transcript(self, *args, **kwargs):
        """Event handler for Deepgram transcript events."""
        if "result" not in kwargs:
            logger.warning("'_on_deepgram_transcript' event received without 'result' in kwargs.")
            return

        result_data = kwargs["result"]

        if not hasattr(result_data, 'channel') or not hasattr(result_data.channel, 'alternatives'):
            logger.warning("'_on_deepgram_transcript' event 'result' is missing expected structure.")
            return

        if len(result_data.channel.alternatives) == 0:
            return

        transcript_chunk = result_data.channel.alternatives[0].transcript
        if transcript_chunk:
            self._process_stt_transcript(transcript_chunk)
            self.update_last_message_time()

    def _process_stt_transcript(self, transcript_chunk: str):
        """Processes STT transcript chunks."""
        if transcript_chunk:
            self.transcript += transcript_chunk + " "
            logger.debug(f"Received STT transcript chunk (via event): '{transcript_chunk.strip()}', accumulated transcript length: {len(self.transcript)}")

    def update_last_message_time(self, *args, **kwargs):
        """Updates the last message time."""
        self.last_message_time = time.time()