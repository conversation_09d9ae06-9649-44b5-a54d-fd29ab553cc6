#!/usr/bin/env python3
"""
Test script to verify the VoxDiscover setup is working correctly.
"""

import asyncio
import os
import sys
import requests
import json
from typing import Dict, Any

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

API_BASE_URL = "http://localhost:7860"

def test_environment():
    """Test that required environment variables are set."""
    print("🔍 Testing environment variables...")
    
    required_vars = [
        "DAILY_API_KEY",
        "SUPABASE_URL", 
        "SUPABASE_KEY",
        "OPENAI_API_KEY"  # At least one LLM service
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_api_connection():
    """Test that the API server is running and responding."""
    print("🔍 Testing API connection...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running and accessible")
            return True
        else:
            print(f"❌ API server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to API server: {e}")
        print("   Make sure the server is running with: python server.py")
        return False

def test_templates():
    """Test that agent templates are available."""
    print("🔍 Testing agent templates...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/templates/", timeout=10)
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ Found {len(templates)} agent templates")
            
            # List available templates
            for template in templates[:3]:  # Show first 3
                print(f"   - {template['name']} ({template['category']})")
            
            if len(templates) > 3:
                print(f"   ... and {len(templates) - 3} more")
            
            return True
        else:
            print(f"❌ Failed to fetch templates: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to fetch templates: {e}")
        return False

def test_agent_creation():
    """Test creating a simple agent."""
    print("🔍 Testing agent creation...")
    
    test_agent = {
        "name": "Test Agent",
        "description": "A test agent created by the setup script",
        "type": "simple",
        "status": "testing",
        "category": "test",
        "stt_service": "deepgram",
        "llm_service": "openai", 
        "tts_service": "elevenlabs",
        "configuration": {
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a test agent. Respond with 'Test successful!' to any input."
                }
            ]
        }
    }
    
    try:
        # Create agent
        response = requests.post(
            f"{API_BASE_URL}/agents/",
            json=test_agent,
            timeout=10
        )
        
        if response.status_code == 201:
            agent_data = response.json()
            agent_id = agent_data.get("id")
            print(f"✅ Successfully created test agent with ID: {agent_id}")
            
            # Clean up - delete the test agent
            delete_response = requests.delete(f"{API_BASE_URL}/agents/{agent_id}")
            if delete_response.status_code == 200:
                print("✅ Successfully cleaned up test agent")
            else:
                print(f"⚠️  Test agent created but cleanup failed (ID: {agent_id})")
            
            return True
        else:
            print(f"❌ Failed to create test agent: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   Error: {error_detail}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to create test agent: {e}")
        return False

def test_supabase_connection():
    """Test Supabase database connection."""
    print("🔍 Testing Supabase connection...")
    
    try:
        # Try to fetch agents (this will test the database connection)
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            print(f"✅ Supabase connection working - found {len(agents)} existing agents")
            return True
        else:
            print(f"❌ Failed to connect to Supabase: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to test Supabase connection: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 VoxDiscover Setup Test")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment),
        ("API Connection", test_api_connection),
        ("Supabase Connection", test_supabase_connection),
        ("Agent Templates", test_templates),
        ("Agent Creation", test_agent_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"💡 Check the setup instructions for {test_name.lower()}")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your VoxDiscover setup is working correctly.")
        print("\n🚀 Next steps:")
        print("1. Start the frontend: cd client && npm run dev")
        print("2. Open http://localhost:3000 in your browser")
        print("3. Create your first agent!")
    else:
        print("⚠️  Some tests failed. Please check the setup instructions.")
        print("   Make sure all environment variables are set and services are accessible.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
