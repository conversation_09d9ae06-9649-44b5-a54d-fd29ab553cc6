'use client';

import { useState } from 'react';
import { X, Plus } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { AgentTemplate } from '../lib/api';

interface TemplateGalleryProps {
  templates: AgentTemplate[];
  onSelectTemplate: (template: AgentTemplate, name: string, description?: string) => void;
  onClose: () => void;
}

export function TemplateGallery({ templates, onSelectTemplate, onClose }: TemplateGalleryProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<AgentTemplate | null>(null);
  const [agentName, setAgentName] = useState('');
  const [agentDescription, setAgentDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['all', ...new Set(templates.map(template => template.category))];
  
  const filteredTemplates = templates.filter(template => 
    selectedCategory === 'all' || template.category === selectedCategory
  );

  const handleCreateFromTemplate = () => {
    if (selectedTemplate && agentName.trim()) {
      onSelectTemplate(selectedTemplate, agentName.trim(), agentDescription.trim() || undefined);
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      customer_service: '🎧',
      sales: '💼',
      scheduling: '📅',
      healthcare: '🏥',
      hospitality: '🍽️',
      technical: '🔧',
      general: '🤖'
    };
    return icons[category] || '🤖';
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Agent Templates</CardTitle>
            <CardDescription>
              Choose from pre-configured agent templates to get started quickly
            </CardDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="overflow-y-auto">
          {!selectedTemplate ? (
            <div>
              {/* Category Filter */}
              <div className="mb-6">
                <div className="flex gap-2 flex-wrap">
                  {categories.map(category => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category === 'all' ? 'All' : category.replace('_', ' ')}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Templates Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTemplates.map((template) => (
                  <Card 
                    key={template.id} 
                    className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-primary/50"
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <CardHeader>
                      <div className="flex items-start gap-3">
                        <div className="text-2xl">
                          {getCategoryIcon(template.category)}
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          <CardDescription className="mt-1">
                            {template.description}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Type:</span>
                          <span className="capitalize">{template.agent_type}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Category:</span>
                          <span className="capitalize">{template.category.replace('_', ' ')}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">STT:</span>
                          <span>{template.stt_service}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">LLM:</span>
                          <span>{template.llm_service}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">TTS:</span>
                          <span>{template.tts_service}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredTemplates.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">No templates found in this category.</p>
                </div>
              )}
            </div>
          ) : (
            <div>
              {/* Template Details and Configuration */}
              <div className="mb-6">
                <Button
                  variant="ghost"
                  onClick={() => setSelectedTemplate(null)}
                  className="mb-4"
                >
                  ← Back to Templates
                </Button>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Template Info */}
                  <Card>
                    <CardHeader>
                      <div className="flex items-start gap-3">
                        <div className="text-3xl">
                          {getCategoryIcon(selectedTemplate.category)}
                        </div>
                        <div>
                          <CardTitle>{selectedTemplate.name}</CardTitle>
                          <CardDescription className="mt-1">
                            {selectedTemplate.description}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium mb-2">Configuration</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Type:</span>
                              <span className="capitalize">{selectedTemplate.agent_type}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Category:</span>
                              <span className="capitalize">{selectedTemplate.category.replace('_', ' ')}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Speech-to-Text:</span>
                              <span>{selectedTemplate.stt_service}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Language Model:</span>
                              <span>{selectedTemplate.llm_service}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Text-to-Speech:</span>
                              <span>{selectedTemplate.tts_service}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">System Message</h4>
                          <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {selectedTemplate.default_config.initial_messages?.[0]?.content || 'No system message configured'}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Agent Configuration */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Create Agent</CardTitle>
                      <CardDescription>
                        Configure your new agent based on this template
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-2">Agent Name *</label>
                          <Input
                            value={agentName}
                            onChange={(e) => setAgentName(e.target.value)}
                            placeholder="Enter a name for your agent"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">Description (Optional)</label>
                          <textarea
                            value={agentDescription}
                            onChange={(e) => setAgentDescription(e.target.value)}
                            placeholder="Customize the description or leave blank to use template description"
                            className="w-full px-3 py-2 border rounded-md bg-background min-h-[100px]"
                          />
                        </div>

                        <Button
                          onClick={handleCreateFromTemplate}
                          disabled={!agentName.trim()}
                          className="w-full"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Create Agent from Template
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
