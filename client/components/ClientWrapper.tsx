'use client';

import { useEffect } from 'react';
import { RTVIClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';

let clientInstance: RTVIClient | null = null;

export function ClientWrapper({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize client only once
    if (!clientInstance) {
      clientInstance = new RTVIClient({
        transport: new DailyTransport(),
        params: {
          baseUrl:
            process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
          endpoints: {
            connect: '/connect',
            action: '/action',
          },
        },
        enableMic: true,
        enableCam: false,
      });
    }

    return () => {
      clientInstance?.disconnect();
    };
  }, []);

  return <>{children}</>;
}