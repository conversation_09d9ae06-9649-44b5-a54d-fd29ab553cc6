'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { ServiceCapability, ServiceConfigurationTemplate, TTS_SERVICES, apiClient } from '../lib/api';

interface TTSConfigPanelProps {
  selectedService: string;
  config: Record<string, any>;
  onConfigChange: (config: Record<string, any>) => void;
  onServiceChange?: (service: string) => void;
}

export function TTSConfigPanel({ selectedService, config, onConfigChange, onServiceChange }: TTSConfigPanelProps) {
  const [serviceCapability, setServiceCapability] = useState<ServiceCapability | null>(null);
  const [templates, setTemplates] = useState<ServiceConfigurationTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTemplate, setActiveTemplate] = useState<string | null>(null);
  const [testAudio, setTestAudio] = useState<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (selectedService) {
      loadServiceCapability();
      loadTemplates();
    }
  }, [selectedService]);

  const loadServiceCapability = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getServiceConfigSchema('tts', selectedService);
      setServiceCapability(response.service);
    } catch (error) {
      console.error('Failed to load service capability:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const response = await apiClient.getServiceTemplates('tts', selectedService);
      setTemplates(response.templates);
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    onConfigChange(newConfig);
  };

  const applyTemplate = (template: ServiceConfigurationTemplate) => {
    onConfigChange(template.configuration);
    setActiveTemplate(template.id);
  };

  const testVoice = async () => {
    // This would integrate with a voice preview API
    console.log('Testing voice with config:', config);
    // Placeholder for voice testing functionality
  };

  const renderConfigField = (key: string, schema: any) => {
    const value = config[key] ?? schema.default;

    if (schema.enum) {
      return (
        <div key={key} className="space-y-2">
          <label className="block text-sm font-medium">{key.replace(/_/g, ' ')}</label>
          <select
            value={value || ''}
            onChange={(e) => handleConfigChange(key, e.target.value)}
            className="w-full px-3 py-2 border rounded-md bg-background"
          >
            <option value="">Select {key}</option>
            {schema.enum.map((option: string) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
          {schema.description && (
            <p className="text-xs text-muted-foreground">{schema.description}</p>
          )}
        </div>
      );
    }

    if (schema.type === 'boolean') {
      return (
        <div key={key} className="space-y-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => handleConfigChange(key, e.target.checked)}
              className="rounded"
            />
            <span className="text-sm font-medium">{key.replace(/_/g, ' ')}</span>
          </label>
          {schema.description && (
            <p className="text-xs text-muted-foreground">{schema.description}</p>
          )}
        </div>
      );
    }

    if (schema.type === 'number') {
      return (
        <div key={key} className="space-y-2">
          <label className="block text-sm font-medium">{key.replace(/_/g, ' ')}</label>
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              value={value || ''}
              onChange={(e) => handleConfigChange(key, parseFloat(e.target.value) || 0)}
              min={schema.minimum}
              max={schema.maximum}
              step={0.1}
              placeholder={`Default: ${schema.default}`}
              className="flex-1"
            />
            {key === 'speed' && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={testVoice}
                className="px-3"
              >
                Test
              </Button>
            )}
          </div>
          {schema.description && (
            <p className="text-xs text-muted-foreground">{schema.description}</p>
          )}
          {(schema.minimum !== undefined || schema.maximum !== undefined) && (
            <p className="text-xs text-muted-foreground">
              Range: {schema.minimum ?? '∞'} - {schema.maximum ?? '∞'}
            </p>
          )}
        </div>
      );
    }

    return (
      <div key={key} className="space-y-2">
        <label className="block text-sm font-medium">{key.replace(/_/g, ' ')}</label>
        <div className="flex items-center space-x-2">
          <Input
            value={value || ''}
            onChange={(e) => handleConfigChange(key, e.target.value)}
            placeholder={`Default: ${schema.default}`}
            className="flex-1"
          />
          {key === 'voice_id' && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={testVoice}
              className="px-3"
            >
              Preview
            </Button>
          )}
        </div>
        {schema.description && (
          <p className="text-xs text-muted-foreground">{schema.description}</p>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading configuration...</div>
        </CardContent>
      </Card>
    );
  }

  if (!serviceCapability) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Select a text-to-speech service to configure
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Service Selection */}
      {onServiceChange && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-lg">🔊</span>
              Text-to-Speech Service
            </CardTitle>
            <CardDescription>
              Choose the service that will convert text responses into natural speech
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {TTS_SERVICES.map((service) => (
                <div
                  key={service.value}
                  className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                    selectedService === service.value
                      ? 'border-primary bg-primary/5 shadow-sm'
                      : 'border-border hover:border-primary/50 hover:bg-muted/50'
                  }`}
                  onClick={() => onServiceChange(service.value)}
                >
                  <div className="font-medium">{service.label}</div>
                  <div className="text-sm text-muted-foreground mt-1">{service.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Service Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {serviceCapability.display_name}
            <div className="flex gap-2">
              {serviceCapability.pricing_tier && (
                <Badge variant="outline">{serviceCapability.pricing_tier}</Badge>
              )}
              {serviceCapability.latency_rating && (
                <Badge variant="outline">{serviceCapability.latency_rating} latency</Badge>
              )}
              {serviceCapability.quality_rating && (
                <Badge variant="outline">{serviceCapability.quality_rating} quality</Badge>
              )}
            </div>
          </CardTitle>
          <CardDescription>{serviceCapability.description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Supported Languages:</strong>
              <div className="flex flex-wrap gap-1 mt-1">
                {serviceCapability.supported_languages.slice(0, 5).map((lang) => (
                  <Badge key={lang} variant="secondary" className="text-xs">
                    {lang}
                  </Badge>
                ))}
                {serviceCapability.supported_languages.length > 5 && (
                  <Badge variant="secondary" className="text-xs">
                    +{serviceCapability.supported_languages.length - 5} more
                  </Badge>
                )}
              </div>
            </div>
            <div>
              <strong>Features:</strong>
              <div className="flex flex-wrap gap-1 mt-1">
                {serviceCapability.features.slice(0, 3).map((feature) => (
                  <Badge key={feature} variant="outline" className="text-xs">
                    {feature.replace(/_/g, ' ')}
                  </Badge>
                ))}
                {serviceCapability.features.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{serviceCapability.features.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Templates */}
      {templates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Voice Presets</CardTitle>
            <CardDescription>
              Pre-configured voice settings for different use cases
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    activeTemplate === template.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => applyTemplate(template)}
                >
                  <div className="font-medium">{template.name}</div>
                  <div className="text-sm text-muted-foreground">{template.description}</div>
                  <div className="flex gap-1 mt-2">
                    {template.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Configuration Fields */}
      <Card>
        <CardHeader>
          <CardTitle>Voice Configuration</CardTitle>
          <CardDescription>
            Customize the voice parameters for your specific needs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {serviceCapability.config_schema?.properties &&
              Object.entries(serviceCapability.config_schema.properties).map(([key, schema]) =>
                renderConfigField(key, schema)
              )}
          </div>

          {/* Voice Test Section */}
          <div className="mt-6 pt-6 border-t">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Voice Test</h4>
                <p className="text-sm text-muted-foreground">
                  Test your voice configuration with sample text
                </p>
              </div>
              <Button onClick={testVoice} variant="outline">
                Test Voice
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
