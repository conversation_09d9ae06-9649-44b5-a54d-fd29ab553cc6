'use client';

import { useState, useEffect } from 'react';
import { X, Play, Square, Mic, MicOff, ExternalLink } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Agent, apiClient } from '../lib/api';
import { PipecatWidget } from './PipecatWidget';

interface AgentTestModalProps {
  agent: Agent;
  onClose: () => void;
}

interface TestSession {
  session_id: string;
  room_url: string;
  token: string;
  test_url: string;
}

export function AgentTestModal({ agent, onClose }: AgentTestModalProps) {
  const [testSession, setTestSession] = useState<TestSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const startTestSession = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (!agent.id) {
        throw new Error('Agent ID is required');
      }

      const session = await apiClient.createTestSession(agent.id);
      setTestSession(session);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start test session');
    } finally {
      setIsLoading(false);
    }
  };

  const openInNewTab = () => {
    if (testSession && agent.id) {
      const testUrl = `${testSession.room_url}?agent_id=${agent.id}`;
      window.open(testUrl, '_blank');
    }
  };

  const endTestSession = () => {
    setTestSession(null);
    setIsConnected(false);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Test Agent: {agent.name}</CardTitle>
            <CardDescription>
              Test your voice agent in a live conversation
            </CardDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="overflow-y-auto">
          <div className="space-y-6">
            {/* Agent Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Agent Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Type:</span>
                    <div className="font-medium capitalize">{agent.type}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">STT:</span>
                    <div className="font-medium">{agent.stt_service || 'Default'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">LLM:</span>
                    <div className="font-medium">{agent.llm_service || 'Default'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">TTS:</span>
                    <div className="font-medium">{agent.tts_service || 'Default'}</div>
                  </div>
                </div>
                
                {agent.configuration?.initial_messages?.[0]?.content && (
                  <div className="mt-4">
                    <span className="text-muted-foreground text-sm">System Message:</span>
                    <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                      {agent.configuration.initial_messages[0].content}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Test Interface */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Voice Test Interface</CardTitle>
                <CardDescription>
                  Start a test session to interact with your agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!testSession ? (
                  <div className="text-center py-8">
                    <div className="mb-4">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Play className="w-8 h-8 text-primary" />
                      </div>
                      <h3 className="text-lg font-medium mb-2">Ready to Test</h3>
                      <p className="text-muted-foreground mb-6">
                        Click the button below to start a test session with your agent
                      </p>
                    </div>
                    
                    {error && (
                      <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md text-destructive text-sm">
                        {error}
                      </div>
                    )}
                    
                    <Button 
                      onClick={startTestSession} 
                      disabled={isLoading}
                      size="lg"
                    >
                      {isLoading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                          Starting Session...
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4 mr-2" />
                          Start Test Session
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Session Info */}
                    <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                        <span className="text-green-700 font-medium">Test session active</span>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={openInNewTab}
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Open in New Tab
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={endTestSession}
                        >
                          <Square className="w-4 h-4 mr-2" />
                          End Session
                        </Button>
                      </div>
                    </div>

                    {/* Voice Interface */}
                    <div className="border rounded-lg p-6 bg-background">
                      <div className="text-center mb-4">
                        <h4 className="font-medium mb-2">Voice Interface</h4>
                        <p className="text-sm text-muted-foreground">
                          Click the microphone to start talking with your agent
                        </p>
                      </div>
                      
                      {/* Embedded Voice Widget */}
                      <div className="flex justify-center">
                        <PipecatWidget agentId={agent.id} testMode={true} />
                      </div>
                      
                      {/* Connection Status */}
                      <div className="mt-4 text-center">
                        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                          isConnected 
                            ? 'bg-green-100 text-green-700' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          <div className={`w-2 h-2 rounded-full ${
                            isConnected ? 'bg-green-500' : 'bg-gray-400'
                          }`} />
                          {isConnected ? 'Connected' : 'Disconnected'}
                        </div>
                      </div>
                    </div>

                    {/* Test Instructions */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">Testing Tips</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="text-sm space-y-2 text-muted-foreground">
                          <li>• Click the microphone button to start/stop recording</li>
                          <li>• Speak clearly and wait for the agent to respond</li>
                          <li>• Test different conversation scenarios</li>
                          <li>• Check if the agent follows its system instructions</li>
                          <li>• Verify the voice quality and response time</li>
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
