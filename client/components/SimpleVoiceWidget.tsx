'use client';

import { RTVIClient, RTVIEvent } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState, useRef } from 'react';
import { Button } from './ui/button';
import { Mic, MicOff, Phone, PhoneOff } from 'lucide-react';

interface SimpleVoiceWidgetProps {
  agentId: string;
  onStatusChange?: (status: string) => void;
}

export function SimpleVoiceWidget({ agentId, onStatusChange }: SimpleVoiceWidgetProps) {
  const [client, setClient] = useState<RTVIClient | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isMicEnabled, setIsMicEnabled] = useState(true);
  const [status, setStatus] = useState('Disconnected');
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const botAudioRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio element
  useEffect(() => {
    if (!botAudioRef.current) {
      botAudioRef.current = document.createElement('audio');
      botAudioRef.current.autoplay = true;
      botAudioRef.current.playsInline = true;
      document.body.appendChild(botAudioRef.current);
    }

    return () => {
      if (botAudioRef.current && document.body.contains(botAudioRef.current)) {
        document.body.removeChild(botAudioRef.current);
      }
    };
  }, []);

  // Log function
  const log = (message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `${timestamp} - ${message}`;
    console.log(logMessage);
    setLogs(prev => [...prev.slice(-9), logMessage]); // Keep last 10 logs
  };

  // Update status
  const updateStatus = (newStatus: string) => {
    setStatus(newStatus);
    onStatusChange?.(newStatus);
    log(`Status: ${newStatus}`);
  };

  // Initialize client
  useEffect(() => {
    if (!agentId) return;

    const initClient = async () => {
      try {
        log('Initializing RTVI client...');

        const rtviClient = new RTVIClient({
          transport: new DailyTransport(),
          params: {
            baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
            endpoints: {
              connect: '/connect',
            },
            requestData: {
              agent_id: agentId,
            },
          },
          enableMic: true,
          enableCam: false,
          callbacks: {
            onConnected: () => {
              setIsConnected(true);
              setIsConnecting(false);
              updateStatus('Connected');
            },
            onDisconnected: () => {
              setIsConnected(false);
              setIsConnecting(false);
              updateStatus('Disconnected');
            },
            onTransportStateChanged: (state) => {
              updateStatus(`Transport: ${state}`);
              if (state === 'ready') {
                setupMediaTracks();
              }
            },
            onBotReady: (data) => {
              log(`Bot ready: ${JSON.stringify(data)}`);
              setupMediaTracks();
            },
            onUserTranscript: (data) => {
              if (data.final) {
                log(`User: ${data.text}`);
              }
            },
            onBotTranscript: (data) => {
              log(`Bot: ${data.text}`);
            },
            onError: (error) => {
              console.error('RTVI Client error:', error);
              setError(error.message || 'Unknown error');
              log(`Error: ${error.message}`);
            },
          },
        });

        // Set up track listeners
        rtviClient.on(RTVIEvent.TrackStarted, (track, participant) => {
          if (!participant?.local) {
            if (track.kind === 'audio') {
              setupAudioTrack(track);
            }
            log(`Track started: ${track.kind} from ${participant?.name || 'unknown'}`);
          }
        });

        rtviClient.on(RTVIEvent.TrackStopped, (track, participant) => {
          if (!participant?.local) {
            log(`Track stopped: ${track.kind} from ${participant?.name || 'unknown'}`);
          }
        });

        // Initialize devices
        await rtviClient.initDevices();
        log('Client initialized successfully');

        setClient(rtviClient);
      } catch (err) {
        console.error('Failed to initialize client:', err);
        setError(err instanceof Error ? err.message : 'Failed to initialize');
        log(`Initialization error: ${err}`);
      }
    };

    initClient();

    return () => {
      if (client) {
        client.disconnect().catch(console.error);
      }
    };
  }, [agentId]);

  // Setup media tracks
  const setupMediaTracks = () => {
    if (!client) return;

    const tracks = client.tracks();
    if (tracks.bot?.audio) {
      setupAudioTrack(tracks.bot.audio);
    }
  };

  // Setup audio track
  const setupAudioTrack = (track: MediaStreamTrack) => {
    if (!botAudioRef.current) return;

    log('Setting up audio track');

    // Check if we're already playing this track
    if (botAudioRef.current.srcObject) {
      const oldTrack = botAudioRef.current.srcObject.getAudioTracks()[0];
      if (oldTrack?.id === track.id) return;
    }

    // Create a new MediaStream with the track and set it as the audio source
    botAudioRef.current.srcObject = new MediaStream([track]);
  };

  // Connect function
  const connect = async () => {
    if (!client || isConnecting) return;

    try {
      setError(null);
      setIsConnecting(true);
      updateStatus('Connecting...');

      log(`Connecting to agent: ${agentId}`);
      await client.connect();
      log('Connection complete');

      // Send initial greeting after connection is established
      setTimeout(() => {
        if (client && isConnected) {
          log('Sending initial greeting');
          try {
            // Use the action system to send a greeting
            client.action({
              type: 'tts_say',
              text: "Hello! I'm an AI voice agent. What can I do for you today?"
            }).catch(err => {
              log(`Failed to send greeting via action: ${err}`);
            });
          } catch (err) {
            log(`Error setting up greeting: ${err}`);
          }
        }
      }, 2000);
    } catch (err) {
      console.error('Connection error:', err);
      setError(err instanceof Error ? err.message : 'Connection failed');
      setIsConnecting(false);
      updateStatus('Error');
      log(`Connection error: ${err}`);
    }
  };

  // Disconnect function
  const disconnect = async () => {
    if (!client) return;

    try {
      log('Disconnecting...');
      await client.disconnect();

      // Clean up audio
      if (botAudioRef.current?.srcObject) {
        const tracks = botAudioRef.current.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        botAudioRef.current.srcObject = null;
      }

      log('Disconnected successfully');
    } catch (err) {
      console.error('Disconnect error:', err);
      log(`Disconnect error: ${err}`);
    }
  };

  // Toggle microphone
  const toggleMic = () => {
    if (!client) return;

    const newMicState = !isMicEnabled;
    client.enableMic(newMicState);
    setIsMicEnabled(newMicState);
    log(newMicState ? 'Microphone enabled' : 'Microphone disabled');
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold mb-2">Voice Agent Test</h3>
        <div className="text-sm text-gray-600">
          Status: <span className={`font-medium ${
            isConnected ? 'text-green-600' :
            isConnecting ? 'text-yellow-600' :
            error ? 'text-red-600' : 'text-gray-600'
          }`}>
            {status}
          </span>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
          <div className="text-red-800 text-sm">{error}</div>
        </div>
      )}

      <div className="flex justify-center gap-4 mb-4">
        {!isConnected ? (
          <Button
            onClick={connect}
            disabled={isConnecting || !client}
            className="flex items-center gap-2"
          >
            <Phone className="w-4 h-4" />
            {isConnecting ? 'Connecting...' : 'Connect'}
          </Button>
        ) : (
          <>
            <Button
              onClick={disconnect}
              variant="destructive"
              className="flex items-center gap-2"
            >
              <PhoneOff className="w-4 h-4" />
              Disconnect
            </Button>
            <Button
              onClick={toggleMic}
              variant={isMicEnabled ? "default" : "secondary"}
              className="flex items-center gap-2"
            >
              {isMicEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
              {isMicEnabled ? 'Mute' : 'Unmute'}
            </Button>
          </>
        )}
      </div>

      {/* Debug logs */}
      <div className="bg-gray-50 rounded-md p-3 max-h-32 overflow-y-auto">
        <div className="text-xs font-medium text-gray-700 mb-2">Debug Log:</div>
        {logs.map((log, index) => (
          <div key={index} className="text-xs text-gray-600 font-mono">
            {log}
          </div>
        ))}
      </div>
    </div>
  );
}
