'use client';

import {
  R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  useRTVI<PERSON>lient,
  useRTVIClientTransportState,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIClient, RTVIEvent } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState, useMemo, useRef } from 'react';
import dynamic from 'next/dynamic';

// 1. Fixed AIVoiceInput dynamic import
const AIVoiceInput = dynamic(
  () => import('./ui/ai-voice-input').then(mod => mod.AIVoiceInput),
  {
    ssr: false,
    loading: () => <div>Loading microphone...</div>
  }
);

interface PipecatWidgetProps {
  agentId?: string;
  testMode?: boolean;
}

export function PipecatWidget({ agentId, testMode = false }: PipecatWidgetProps) {
  // Create client with agent-specific configuration
  // Use useMemo to recreate client when agentId changes
  const client = useMemo(() => {
    if (!agentId && !testMode) return null;

    console.log('PipecatWidget: Creating RTVI client for agent:', agentId);

    const rtviClient = new RTVIClient({
      transport: new DailyTransport(),
      params: {
        baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
        endpoints: {
          connect: '/connect',
          action: '/action'
        },
        requestData: agentId ? { agent_id: agentId } : undefined
      },
      enableMic: true,
      enableCam: false,
      callbacks: {
        onConnected: () => {
          console.log('PipecatWidget: RTVI Client connected');
        },
        onDisconnected: () => {
          console.log('PipecatWidget: RTVI Client disconnected');
        },
        onTransportStateChanged: (state) => {
          console.log('PipecatWidget: Transport state changed:', state);
        },
        onBotReady: (data) => {
          console.log('PipecatWidget: Bot ready:', data);
        },
        onUserTranscript: (data) => {
          if (data.final) {
            console.log('PipecatWidget: User said:', data.text);
          }
        },
        onBotTranscript: (data) => {
          console.log('PipecatWidget: Bot said:', data.text);
        },
        onError: (error) => {
          console.error('PipecatWidget: RTVI Client error:', error);
        }
      }
    });

    return rtviClient;
  }, [agentId, testMode]);

  if (!client) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-sm text-muted-foreground">
          {!agentId && !testMode ? 'No agent selected' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <RTVIClientProvider client={client}>
      <WidgetContent testMode={testMode} agentId={agentId} />
    </RTVIClientProvider>
  );
}

interface WidgetContentProps {
  testMode?: boolean;
  agentId?: string;
}

function WidgetContent({ testMode = false, agentId }: WidgetContentProps) {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const operationInProgress = useRef(false);

  // Initialize audio element for bot audio
  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = document.createElement('audio');
      audioRef.current.autoplay = true;
      audioRef.current.playsInline = true;
      document.body.appendChild(audioRef.current);
    }

    return () => {
      if (audioRef.current && document.body.contains(audioRef.current)) {
        // Clean up audio tracks before removing element
        if (audioRef.current.srcObject) {
          const tracks = audioRef.current.srcObject.getTracks();
          tracks.forEach(track => track.stop());
          audioRef.current.srcObject = null;
        }
        document.body.removeChild(audioRef.current);
        audioRef.current = null;
      }
    };
  }, []);

  // Setup audio track (same as SimpleVoiceWidget)
  const setupAudioTrack = (track: MediaStreamTrack) => {
    if (!audioRef.current) return;

    console.log('PipecatWidget: Setting up audio track');

    // Check if we're already playing this track
    if (audioRef.current.srcObject) {
      const oldTrack = audioRef.current.srcObject.getAudioTracks()[0];
      if (oldTrack?.id === track.id) return;
    }

    // Create a new MediaStream with the track and set it as the audio source
    audioRef.current.srcObject = new MediaStream([track]);
  };

  // Setup media tracks (same as SimpleVoiceWidget)
  const setupMediaTracks = () => {
    if (!client) return;

    const tracks = client.tracks();
    if (tracks.bot?.audio) {
      setupAudioTrack(tracks.bot.audio);
    }
  };

  // Initialize client and set up event listeners (simplified like SimpleVoiceWidget)
  useEffect(() => {
    if (!client) {
      console.log('PipecatWidget: No client available');
      return;
    }

    console.log('PipecatWidget: Starting initialization...');

    const initializeClient = async () => {
      try {
        console.log('PipecatWidget: Initializing RTVI client...');

        // Set up track listeners first (these don't require device access)
        client.on(RTVIEvent.TrackStarted, (track, participant) => {
          console.log('PipecatWidget: Track started:', track.kind, participant?.name);
          if (!participant?.local && track.kind === 'audio') {
            setupAudioTrack(track);
          }
        });

        client.on(RTVIEvent.TrackStopped, (track, participant) => {
          console.log('PipecatWidget: Track stopped:', track.kind, participant?.name);
        });

        // Try to initialize devices, but don't fail if it times out
        try {
          const initPromise = client.initDevices();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Device initialization timeout')), 5000)
          );

          await Promise.race([initPromise, timeoutPromise]);
          console.log('PipecatWidget: Device initialization completed');
        } catch (deviceErr) {
          console.log('PipecatWidget: Device initialization will happen during connect (this is normal)');
          // Don't set error here - devices can be initialized later during connect
        }

        setIsInitialized(true);
        console.log('PipecatWidget: RTVI client initialized successfully');
      } catch (err) {
        console.error('PipecatWidget: Failed to initialize client:', err);
        setError(`Failed to initialize: ${err instanceof Error ? err.message : 'Unknown error'}`);
        // Set initialized to true anyway to show the UI
        setIsInitialized(true);
      }
    };

    initializeClient();

    // Cleanup only when component unmounts or client changes
    return () => {
      if (client) {
        console.log('PipecatWidget: Cleaning up - disconnecting client');
        client.disconnect().catch(console.error);
      }
    };
  }, [client]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle transport state changes (setup media when ready)
  useEffect(() => {
    if (transportState === 'ready') {
      console.log('PipecatWidget: Transport ready, setting up media tracks');
      setupMediaTracks();
    }
  }, [transportState]);

  if (!isMounted) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-sm text-muted-foreground">
          Loading...
        </div>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-sm text-muted-foreground">
          Initializing audio devices...
        </div>
      </div>
    );
  }

  const containerClass = testMode
    ? "flex flex-col items-center gap-4"
    : "fixed bottom-8 right-8 flex flex-col items-end gap-4";

  const isConnected = ['connected', 'ready'].includes(transportState);
  const canConnect = (agentId || testMode);

  const handleToggle = async (isActive: boolean) => {
    // Check if an operation is already in progress
    // if (operationInProgress.current) {
    //   console.log('PipecatWidget: Operation in progress, ignoring toggle');
    //   return;
    // }

    try {
      setError(null);
      operationInProgress.current = true;
      setIsConnecting(true);

      if (isActive) {
        if (!canConnect) {
          setError('Please select an agent to start conversation');
          operationInProgress.current = false;
          setIsConnecting(false);
          return;
        }

        console.log('PipecatWidget: Starting connection...');

        // Initialize devices if not already done (fallback)
        try {
          await client.initDevices();
          console.log('PipecatWidget: Devices initialized during connect');
        } catch (deviceErr) {
          console.warn('PipecatWidget: Device init during connect failed, continuing:', deviceErr);
        }

        // Connect to the bot
        await client.connect();
        console.log('PipecatWidget: Connection successful');

        // Send initial greeting after connection is established
        setTimeout(() => {
          if (client && ['connected', 'ready'].includes(transportState)) {
            console.log('PipecatWidget: Sending initial greeting');
            try {
              // Use the action system to send a greeting
              client.action({
                type: 'tts_say',
                text: "Hello! I'm an AI voice agent. What can I do for you today?"
              }).catch(err => {
                console.warn('PipecatWidget: Failed to send greeting via action:', err);
                // Fallback: try sending as a message
                client.sendMessage({
                  type: 'conversation.item.create',
                  item: {
                    type: 'message',
                    role: 'assistant',
                    content: [{
                      type: 'text',
                      text: "Hello! I'm an AI voice agent. What can I do for you today?"
                    }]
                  }
                }).catch(msgErr => {
                  console.warn('PipecatWidget: Failed to send greeting via message:', msgErr);
                });
              });
            } catch (err) {
              console.warn('PipecatWidget: Error setting up greeting:', err);
            }
          }
        }, 2000);

      } else {
        console.log('PipecatWidget: Starting disconnection...');

        // Disconnect from the bot
        await client.disconnect();
        console.log('PipecatWidget: Disconnect call completed');

        // Clean up audio tracks
        if (audioRef.current?.srcObject) {
          console.log('PipecatWidget: Cleaning up audio tracks');
          const tracks = audioRef.current.srcObject.getTracks();
          tracks.forEach(track => {
            console.log('PipecatWidget: Stopping track:', track.kind, track.id);
            track.stop();
          });
          audioRef.current.srcObject = null;
        }

        console.log('PipecatWidget: Disconnected successfully');
      }
    } catch (err) {
      console.error('PipecatWidget: Toggle error:', err);
      setError(
        err instanceof Error ? err.message : 'Operation failed'
      );
    } finally {
      operationInProgress.current = false;
      setIsConnecting(false);
    }
  };

  return (
    <div className={containerClass}>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        {error && (
          <div className="text-red-500 text-sm p-2 max-w-xs">
            {error}
          </div>
        )}
        {!canConnect && (
          <div className="text-yellow-600 text-sm p-2 max-w-xs">
            No agent selected. Please select an agent to start conversation.
          </div>
        )}
        {canConnect && !isInitialized && (
          <div className="text-blue-600 text-sm p-2 max-w-xs">
            Initializing audio devices...
          </div>
        )}
        {isConnecting && (
          <div className="text-blue-600 text-sm p-2">
            {isConnected ? 'Disconnecting...' : 'Connecting to agent...'}
          </div>
        )}
        {transportState && (
          <div className="text-xs text-gray-500 p-2">
            Status: {transportState}
          </div>
        )}
        <AIVoiceInput
          isActive={isConnected}
          onChange={handleToggle}
          className="w-auto"
          demoMode={false}
          disabled={!canConnect || !isInitialized || isConnecting}
        />
      </div>
      <RTVIClientAudio />
    </div>
  );
}