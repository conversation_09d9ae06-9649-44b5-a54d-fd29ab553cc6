'use client';

import { useState, useEffect } from 'react';
import { Plus, Search, Filter, Play, Edit, Trash2, MoreHorizontal, Mic, Brain, Volume2, Settings, Users, Activity } from 'lucide-react';
import { <PERSON><PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Agent, AgentTemplate, apiClient } from '../lib/api';
import { AgentForm } from './AgentForm';
import { TemplateGallery } from './TemplateGallery';
import { AgentTestModal } from './AgentTestModal';
import { PipecatWidget } from './PipecatWidget';

export function AgentDashboard() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAgentForm, setShowAgentForm] = useState(false);
  const [showTemplateGallery, setShowTemplateGallery] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [testingAgent, setTestingAgent] = useState<Agent | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [agentsData, templatesData] = await Promise.all([
        apiClient.getAgents(),
        apiClient.getTemplates()
      ]);
      setAgents(agentsData);
      setTemplates(templatesData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAgent = async (agentData: Omit<Agent, 'id'>) => {
    try {
      const newAgent = await apiClient.createAgent(agentData);
      setAgents([...agents, newAgent]);
      setShowAgentForm(false);
    } catch (error) {
      console.error('Failed to create agent:', error);
    }
  };

  const handleUpdateAgent = async (agentData: Partial<Agent>) => {
    if (!editingAgent?.id) return;

    try {
      const updatedAgent = await apiClient.updateAgent(editingAgent.id, agentData);
      setAgents(agents.map(agent =>
        agent.id === editingAgent.id ? updatedAgent : agent
      ));
      setEditingAgent(null);
      setShowAgentForm(false);
    } catch (error) {
      console.error('Failed to update agent:', error);
    }
  };

  const handleSaveAgent = async (agentData: Omit<Agent, 'id'> | Partial<Agent>) => {
    if (editingAgent) {
      await handleUpdateAgent(agentData as Partial<Agent>);
    } else {
      await handleCreateAgent(agentData as Omit<Agent, 'id'>);
    }
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (!confirm('Are you sure you want to delete this agent?')) return;

    try {
      await apiClient.deleteAgent(agentId);
      setAgents(agents.filter(agent => agent.id !== agentId));
    } catch (error) {
      console.error('Failed to delete agent:', error);
    }
  };

  const handleTestAgent = (agent: Agent) => {
    setTestingAgent(agent);
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || agent.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = ['all', ...new Set(agents.map(agent => agent.category).filter(Boolean))];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto p-6">
        {/* Enhanced Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Voice AI Agents
              </h1>
              <p className="text-muted-foreground text-lg mt-2">
                Build, manage, and deploy intelligent voice assistants
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowTemplateGallery(true)}
                className="shadow-sm hover:shadow-md transition-shadow"
              >
                <Plus className="w-4 h-4 mr-2" />
                From Template
              </Button>
              <Button
                onClick={() => setShowAgentForm(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-sm hover:shadow-md transition-all"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Agent
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Agents</p>
                    <p className="text-2xl font-bold">{agents.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <Activity className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Active</p>
                    <p className="text-2xl font-bold">{agents.filter(a => a.status === 'active').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <Mic className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Calls</p>
                    <p className="text-2xl font-bold">{agents.reduce((sum, a) => sum + (a.total_calls || 0), 0)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                    <Settings className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Categories</p>
                    <p className="text-2xl font-bold">{categories.length - 1}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enhanced Filters and Agent Selector */}
        <div className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 mb-6 border-0 shadow-sm">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search agents by name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/80 dark:bg-slate-700/80 border-0 shadow-sm"
                />
              </div>
            </div>
            <div className="flex gap-3">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border-0 rounded-lg bg-white/80 dark:bg-slate-700/80 shadow-sm min-w-[140px]"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
              <select
                value={selectedAgentId || ''}
                onChange={(e) => setSelectedAgentId(e.target.value || null)}
                className="px-4 py-2 border-0 rounded-lg bg-white/80 dark:bg-slate-700/80 shadow-sm min-w-[200px]"
              >
                <option value="">🎤 Select agent for voice chat</option>
                {agents.filter(agent => agent.status === 'active').map(agent => (
                  <option key={agent.id} value={agent.id}>
                    {agent.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Enhanced Agents Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAgents.map((agent) => (
            <Card key={agent.id} className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-0 shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-[1.02] group">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                        {agent.name}
                      </CardTitle>
                      <div className={`w-2 h-2 rounded-full ${
                        agent.status === 'active' ? 'bg-green-500' :
                        agent.status === 'inactive' ? 'bg-red-500' :
                        'bg-yellow-500'
                      }`} />
                    </div>
                    <CardDescription className="text-sm line-clamp-2">
                      {agent.description || 'No description provided'}
                    </CardDescription>
                  </div>
                  <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleTestAgent(agent)}
                      title="Test Agent"
                      className="h-8 w-8 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                    >
                      <Play className="w-4 h-4 text-blue-600" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        setEditingAgent(agent);
                        setShowAgentForm(true);
                      }}
                      title="Edit Agent"
                      className="h-8 w-8 hover:bg-purple-100 dark:hover:bg-purple-900/30"
                    >
                      <Edit className="w-4 h-4 text-purple-600" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => agent.id && handleDeleteAgent(agent.id)}
                      title="Delete Agent"
                      className="h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900/30"
                    >
                      <Trash2 className="w-4 h-4 text-red-600" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {/* Service Stack */}
                <div className="flex items-center gap-2 mb-4 p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                  <div className="flex items-center gap-1 text-xs">
                    <Mic className="w-3 h-3 text-blue-500" />
                    <span className="font-medium">{agent.stt_service || 'STT'}</span>
                  </div>
                  <div className="w-px h-4 bg-border" />
                  <div className="flex items-center gap-1 text-xs">
                    <Brain className="w-3 h-3 text-purple-500" />
                    <span className="font-medium">{agent.llm_service || 'LLM'}</span>
                  </div>
                  <div className="w-px h-4 bg-border" />
                  <div className="flex items-center gap-1 text-xs">
                    <Volume2 className="w-3 h-3 text-green-500" />
                    <span className="font-medium">{agent.tts_service || 'TTS'}</span>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Type:</span>
                    <span className="capitalize font-medium">{agent.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Category:</span>
                    <span className="capitalize font-medium">{agent.category || 'general'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Calls:</span>
                    <span className="font-medium">{agent.total_calls || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Duration:</span>
                    <span className="font-medium">{Math.round((agent.total_duration || 0) / 60)}m</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredAgents.length === 0 && (
          <div className="text-center py-16">
            <div className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-xl p-8 max-w-md mx-auto">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No agents found</h3>
              <p className="text-muted-foreground mb-6">
                {searchTerm || selectedCategory !== 'all'
                  ? 'No agents match your current filters. Try adjusting your search criteria.'
                  : 'Get started by creating your first voice AI agent.'
                }
              </p>
              <Button
                onClick={() => setShowAgentForm(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Agent
              </Button>
            </div>
          </div>
        )}

        {/* Voice Widget */}
        <PipecatWidget agentId={selectedAgentId || undefined} />
      </div>

      {/* Modals */}
      {showAgentForm && (
        <AgentForm
          agent={editingAgent}
          onSave={handleSaveAgent}
          onCancel={() => {
            setShowAgentForm(false);
            setEditingAgent(null);
          }}
        />
      )}

      {showTemplateGallery && (
        <TemplateGallery
          templates={templates}
          onSelectTemplate={async (template, name, description) => {
            try {
              const newAgent = await apiClient.createAgentFromTemplate(template.id, name, description);
              setAgents([...agents, newAgent]);
              setShowTemplateGallery(false);
            } catch (error) {
              console.error('Failed to create agent from template:', error);
            }
          }}
          onClose={() => setShowTemplateGallery(false)}
        />
      )}

      {testingAgent && (
        <AgentTestModal
          agent={testingAgent}
          onClose={() => setTestingAgent(null)}
        />
      )}
    </div>
  );
}
