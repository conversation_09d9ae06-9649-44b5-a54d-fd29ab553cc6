'use client';

import { useState, useEffect } from 'react';
import { Plus, Search, Filter, Play, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Agent, AgentTemplate, apiClient } from '../lib/api';
import { AgentForm } from './AgentForm';
import { TemplateGallery } from './TemplateGallery';
import { AgentTestModal } from './AgentTestModal';
import { PipecatWidget } from './PipecatWidget';

export function AgentDashboard() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAgentForm, setShowAgentForm] = useState(false);
  const [showTemplateGallery, setShowTemplateGallery] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);
  const [testingAgent, setTestingAgent] = useState<Agent | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [agentsData, templatesData] = await Promise.all([
        apiClient.getAgents(),
        apiClient.getTemplates()
      ]);
      setAgents(agentsData);
      setTemplates(templatesData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAgent = async (agentData: Omit<Agent, 'id'>) => {
    try {
      const newAgent = await apiClient.createAgent(agentData);
      setAgents([...agents, newAgent]);
      setShowAgentForm(false);
    } catch (error) {
      console.error('Failed to create agent:', error);
    }
  };

  const handleUpdateAgent = async (agentData: Partial<Agent>) => {
    if (!editingAgent?.id) return;

    try {
      const updatedAgent = await apiClient.updateAgent(editingAgent.id, agentData);
      setAgents(agents.map(agent =>
        agent.id === editingAgent.id ? updatedAgent : agent
      ));
      setEditingAgent(null);
    } catch (error) {
      console.error('Failed to update agent:', error);
    }
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (!confirm('Are you sure you want to delete this agent?')) return;

    try {
      await apiClient.deleteAgent(agentId);
      setAgents(agents.filter(agent => agent.id !== agentId));
    } catch (error) {
      console.error('Failed to delete agent:', error);
    }
  };

  const handleTestAgent = (agent: Agent) => {
    setTestingAgent(agent);
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || agent.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = ['all', ...new Set(agents.map(agent => agent.category).filter(Boolean))];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Voice AI Agents</h1>
          <p className="text-muted-foreground">Manage and test your voice AI agents</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowTemplateGallery(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            From Template
          </Button>
          <Button onClick={() => setShowAgentForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Agent
          </Button>
        </div>
      </div>

      {/* Filters and Agent Selector */}
      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border rounded-md bg-background"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'All Categories' : category}
            </option>
          ))}
        </select>
        <select
          value={selectedAgentId || ''}
          onChange={(e) => setSelectedAgentId(e.target.value || null)}
          className="px-3 py-2 border rounded-md bg-background min-w-[200px]"
        >
          <option value="">Select agent for voice chat</option>
          {agents.filter(agent => agent.status === 'active').map(agent => (
            <option key={agent.id} value={agent.id}>
              {agent.name}
            </option>
          ))}
        </select>
      </div>

      {/* Agents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAgents.map((agent) => (
          <Card key={agent.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{agent.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {agent.description || 'No description'}
                  </CardDescription>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleTestAgent(agent)}
                    title="Test Agent"
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setEditingAgent(agent);
                      setShowAgentForm(true);
                    }}
                    title="Edit Agent"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => agent.id && handleDeleteAgent(agent.id)}
                    title="Delete Agent"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="capitalize">{agent.type}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className={`capitalize ${
                    agent.status === 'active' ? 'text-green-600' :
                    agent.status === 'inactive' ? 'text-red-600' :
                    'text-yellow-600'
                  }`}>
                    {agent.status || 'active'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Category:</span>
                  <span className="capitalize">{agent.category || 'general'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Total Calls:</span>
                  <span>{agent.total_calls || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAgents.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No agents found matching your criteria.</p>
          <Button
            className="mt-4"
            onClick={() => setShowAgentForm(true)}
          >
            Create Your First Agent
          </Button>
        </div>
      )}

      {/* Modals */}
      {showAgentForm && (
        <AgentForm
          agent={editingAgent}
          onSave={editingAgent ? handleUpdateAgent : handleCreateAgent}
          onCancel={() => {
            setShowAgentForm(false);
            setEditingAgent(null);
          }}
        />
      )}

      {showTemplateGallery && (
        <TemplateGallery
          templates={templates}
          onSelectTemplate={async (template, name, description) => {
            try {
              const newAgent = await apiClient.createAgentFromTemplate(template.id, name, description);
              setAgents([...agents, newAgent]);
              setShowTemplateGallery(false);
            } catch (error) {
              console.error('Failed to create agent from template:', error);
            }
          }}
          onClose={() => setShowTemplateGallery(false)}
        />
      )}

      {testingAgent && (
        <AgentTestModal
          agent={testingAgent}
          onClose={() => setTestingAgent(null)}
        />
      )}

      {/* Voice Widget */}
      <PipecatWidget agentId={selectedAgentId || undefined} />
    </div>
  );
}
