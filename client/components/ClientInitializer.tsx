"use client";

import { useEffect } from "react";
import { useRTVIClient } from "@pipecat-ai/client-react";

export function ClientInitializer() {
  const client = useRTVIClient();

  useEffect(() => {
    if (client) {
      // Update parameters using documented properties
      client.params = {
        ...client.params,
        baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:7860"
      };
    }
  }, [client]);

  return null;
}