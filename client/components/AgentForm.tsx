'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Agent, STT_SERVICES, LLM_SERVICES, TTS_SERVICES, AGENT_TYPES, AGENT_STATUSES } from '../lib/api';
import { STTConfigPanel } from './STTConfigPanel';
import { LLMConfigPanel } from './LLMConfigPanel';
import { TTSConfigPanel } from './TTSConfigPanel';

interface AgentFormProps {
  agent?: Agent | null;
  onSave: (agent: Omit<Agent, 'id'> | Partial<Agent>) => void;
  onCancel: () => void;
}

export function AgentForm({ agent, onSave, onCancel }: AgentFormProps) {
  const [formData, setFormData] = useState<Partial<Agent>>({
    name: '',
    description: '',
    type: 'simple',
    status: 'active',
    category: 'general',
    stt_service: 'deepgram',
    llm_service: 'openai',
    tts_service: 'elevenlabs',
    stt_config: {},
    llm_config: {},
    tts_config: {},
    vad_config: {},
    configuration: {
      initial_messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant. Keep responses short and to the point.'
        }
      ]
    }
  });

  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (agent) {
      setFormData(agent);
    }
  }, [agent]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleInputChange = (field: keyof Agent, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleConfigChange = (configType: 'stt_config' | 'llm_config' | 'tts_config' | 'vad_config', key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [configType]: {
        ...prev[configType],
        [key]: value
      }
    }));
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info' },
    { id: 'services', label: 'Services' },
    { id: 'stt_config', label: 'Speech-to-Text' },
    { id: 'llm_config', label: 'Language Model' },
    { id: 'tts_config', label: 'Text-to-Speech' },
    { id: 'conversation', label: 'Conversation' },
    { id: 'advanced', label: 'Advanced' }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{agent ? 'Edit Agent' : 'Create New Agent'}</CardTitle>
            <CardDescription>
              {agent ? 'Update your agent configuration' : 'Configure your new voice AI agent'}
            </CardDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={onCancel}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="overflow-y-auto flex-1 min-h-0 px-6 py-4">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Tabs */}
            <div className="flex border-b">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Basic Info Tab */}
            {activeTab === 'basic' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Name *</label>
                  <Input
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter agent name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Description</label>
                  <textarea
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe what this agent does"
                    className="w-full px-3 py-2 border rounded-md bg-background min-h-[100px]"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Type</label>
                    <select
                      value={formData.type || 'simple'}
                      onChange={(e) => handleInputChange('type', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    >
                      {AGENT_TYPES.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Status</label>
                    <select
                      value={formData.status || 'active'}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    >
                      {AGENT_STATUSES.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <Input
                    value={formData.category || ''}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    placeholder="e.g., customer_service, sales, support"
                  />
                </div>
              </div>
            )}

            {/* Services Tab */}
            {activeTab === 'services' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Service Selection</h3>
                  <p className="text-sm text-muted-foreground mb-6">
                    Choose the AI services that power your voice agent. Each service can be configured in detail in the dedicated configuration tabs.
                  </p>

                  <div className="space-y-6">
                    {/* STT Service Selection */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">Speech-to-Text Service</CardTitle>
                        <CardDescription>
                          Converts spoken words into text for processing
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <select
                          value={formData.stt_service || 'deepgram'}
                          onChange={(e) => handleInputChange('stt_service', e.target.value)}
                          className="w-full px-3 py-2 border rounded-md bg-background"
                        >
                          {STT_SERVICES.map(service => (
                            <option key={service.value} value={service.value}>
                              {service.label} - {service.description}
                            </option>
                          ))}
                        </select>
                      </CardContent>
                    </Card>

                    {/* LLM Service Selection */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">Language Model Service</CardTitle>
                        <CardDescription>
                          Processes and generates intelligent responses
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <select
                          value={formData.llm_service || 'openai'}
                          onChange={(e) => handleInputChange('llm_service', e.target.value)}
                          className="w-full px-3 py-2 border rounded-md bg-background"
                        >
                          {LLM_SERVICES.map(service => (
                            <option key={service.value} value={service.value}>
                              {service.label} - {service.description}
                            </option>
                          ))}
                        </select>
                      </CardContent>
                    </Card>

                    {/* TTS Service Selection */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">Text-to-Speech Service</CardTitle>
                        <CardDescription>
                          Converts text responses into natural-sounding speech
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <select
                          value={formData.tts_service || 'elevenlabs'}
                          onChange={(e) => handleInputChange('tts_service', e.target.value)}
                          className="w-full px-3 py-2 border rounded-md bg-background"
                        >
                          {TTS_SERVICES.map(service => (
                            <option key={service.value} value={service.value}>
                              {service.label} - {service.description}
                            </option>
                          ))}
                        </select>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Next Steps</h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    After selecting your services, configure each one in the dedicated tabs above.
                    Each service has specific parameters that can be tuned for optimal performance.
                  </p>
                </div>
              </div>
            )}

            {/* STT Configuration Tab */}
            {activeTab === 'stt_config' && (
              <STTConfigPanel
                selectedService={formData.stt_service || 'deepgram'}
                config={formData.stt_config || {}}
                onConfigChange={(config) => handleInputChange('stt_config', config)}
              />
            )}

            {/* LLM Configuration Tab */}
            {activeTab === 'llm_config' && (
              <LLMConfigPanel
                selectedService={formData.llm_service || 'openai'}
                config={formData.llm_config || {}}
                onConfigChange={(config) => handleInputChange('llm_config', config)}
              />
            )}

            {/* TTS Configuration Tab */}
            {activeTab === 'tts_config' && (
              <TTSConfigPanel
                selectedService={formData.tts_service || 'elevenlabs'}
                config={formData.tts_config || {}}
                onConfigChange={(config) => handleInputChange('tts_config', config)}
              />
            )}

            {/* Conversation Configuration Tab */}
            {activeTab === 'conversation' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Conversation Flow</CardTitle>
                    <CardDescription>
                      Configure how the agent handles conversation flow and interactions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">System Message</label>
                      <textarea
                        value={formData.configuration?.initial_messages?.[0]?.content || ''}
                        onChange={(e) => {
                          const newConfig = {
                            ...formData.configuration,
                            initial_messages: [
                              {
                                role: 'system',
                                content: e.target.value
                              }
                            ]
                          };
                          handleInputChange('configuration', newConfig);
                        }}
                        placeholder="Enter the system message that defines the agent's behavior"
                        className="w-full px-3 py-2 border rounded-md bg-background min-h-[150px]"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.conversation_config?.interruption_enabled ?? true}
                            onChange={(e) => handleConfigChange('conversation_config', 'interruption_enabled', e.target.checked)}
                            className="rounded"
                          />
                          <span className="text-sm font-medium">Allow Interruptions</span>
                        </label>
                        <p className="text-xs text-muted-foreground mt-1">
                          Allow users to interrupt the agent while speaking
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Response Timeout (ms)</label>
                        <Input
                          type="number"
                          value={formData.conversation_config?.response_timeout || 5000}
                          onChange={(e) => handleConfigChange('conversation_config', 'response_timeout', parseInt(e.target.value))}
                          min={1000}
                          max={30000}
                          step={1000}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Advanced Tab */}
            {activeTab === 'advanced' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Webhook URL</label>
                  <Input
                    value={formData.webhook_url || ''}
                    onChange={(e) => handleInputChange('webhook_url', e.target.value)}
                    placeholder="https://your-webhook-url.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Tags (comma-separated)</label>
                  <Input
                    value={formData.tags?.join(', ') || ''}
                    onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()))}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit">
                {agent ? 'Update Agent' : 'Create Agent'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
