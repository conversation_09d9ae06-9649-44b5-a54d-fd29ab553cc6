'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Agent, AGENT_TYPES, AGENT_STATUSES } from '../lib/api';
import { STTConfigPanel } from './STTConfigPanel';
import { LLMConfigPanel } from './LLMConfigPanel';
import { TTSConfigPanel } from './TTSConfigPanel';

interface AgentFormProps {
  agent?: Agent | null;
  onSave: (agent: Omit<Agent, 'id'> | Partial<Agent>) => Promise<void> | void;
  onCancel: () => void;
}

export function AgentForm({ agent, onSave, onCancel }: AgentFormProps) {
  const [formData, setFormData] = useState<Partial<Agent>>({
    name: '',
    description: '',
    type: 'simple',
    status: 'active',
    category: 'general',
    stt_service: 'deepgram',
    llm_service: 'openai',
    tts_service: 'elevenlabs',
    stt_config: {},
    llm_config: {},
    tts_config: {},
    vad_config: {},
    configuration: {
      initial_messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant. Keep responses short and to the point.'
        }
      ]
    }
  });

  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (agent) {
      setFormData(agent);
    }
  }, [agent]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
  };

  const handleInputChange = (field: keyof Agent, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleConfigChange = (configType: 'stt_config' | 'llm_config' | 'tts_config' | 'vad_config' | 'conversation_config', key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [configType]: {
        ...(prev[configType] || {}),
        [key]: value
      }
    }));
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '📝' },
    { id: 'stt_config', label: 'Speech-to-Text', icon: '🎤' },
    { id: 'llm_config', label: 'Language Model', icon: '🧠' },
    { id: 'tts_config', label: 'Text-to-Speech', icon: '🔊' },
    { id: 'conversation', label: 'Conversation', icon: '💬' },
    { id: 'advanced', label: 'Advanced', icon: '⚙️' }
  ];

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-5xl max-h-[90vh] flex flex-col bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-0 shadow-2xl">
        <CardHeader className="flex flex-row items-center justify-between border-b bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50">
          <div>
            <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {agent ? 'Edit Agent' : 'Create New Agent'}
            </CardTitle>
            <CardDescription className="text-base mt-1">
              {agent ? 'Update your agent configuration and settings' : 'Configure your new voice AI agent with advanced settings'}
            </CardDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={onCancel} className="hover:bg-red-100 dark:hover:bg-red-900/30">
            <X className="w-5 h-5" />
          </Button>
        </CardHeader>

        <CardContent className="overflow-y-auto flex-1 min-h-0 px-6 py-4">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Enhanced Tabs */}
            <div className="flex border-b border-border/40 bg-muted/30 rounded-t-lg p-1 mb-6">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-background text-foreground shadow-sm border border-border/50'
                      : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                  }`}
                >
                  <span className="text-base">{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Basic Info Tab */}
            {activeTab === 'basic' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Name *</label>
                  <Input
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter agent name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Description</label>
                  <textarea
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe what this agent does"
                    className="w-full px-3 py-2 border rounded-md bg-background min-h-[100px]"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Type</label>
                    <select
                      value={formData.type || 'simple'}
                      onChange={(e) => handleInputChange('type', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    >
                      {AGENT_TYPES.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Status</label>
                    <select
                      value={formData.status || 'active'}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    >
                      {AGENT_STATUSES.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <Input
                    value={formData.category || ''}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    placeholder="e.g., customer_service, sales, support"
                  />
                </div>
              </div>
            )}



            {/* STT Configuration Tab */}
            {activeTab === 'stt_config' && (
              <STTConfigPanel
                selectedService={formData.stt_service || 'deepgram'}
                config={formData.stt_config || {}}
                onConfigChange={(config) => handleInputChange('stt_config', config)}
                onServiceChange={(service) => handleInputChange('stt_service', service)}
              />
            )}

            {/* LLM Configuration Tab */}
            {activeTab === 'llm_config' && (
              <LLMConfigPanel
                selectedService={formData.llm_service || 'openai'}
                config={formData.llm_config || {}}
                onConfigChange={(config) => handleInputChange('llm_config', config)}
                onServiceChange={(service) => handleInputChange('llm_service', service)}
              />
            )}

            {/* TTS Configuration Tab */}
            {activeTab === 'tts_config' && (
              <TTSConfigPanel
                selectedService={formData.tts_service || 'elevenlabs'}
                config={formData.tts_config || {}}
                onConfigChange={(config) => handleInputChange('tts_config', config)}
                onServiceChange={(service) => handleInputChange('tts_service', service)}
              />
            )}

            {/* Conversation Configuration Tab */}
            {activeTab === 'conversation' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Conversation Flow</CardTitle>
                    <CardDescription>
                      Configure how the agent handles conversation flow and interactions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Starting Phrase</label>
                      <textarea
                        value={formData.conversation_config?.starting_phrase || ''}
                        onChange={(e) => handleConfigChange('conversation_config', 'starting_phrase', e.target.value)}
                        placeholder="Hello! I'm here to help. What can I do for you?"
                        className="w-full px-3 py-2 border rounded-md bg-background min-h-[80px]"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Optional greeting message said when the conversation starts
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">System Message</label>
                      <textarea
                        value={formData.configuration?.initial_messages?.[0]?.content || ''}
                        onChange={(e) => {
                          const newConfig = {
                            ...formData.configuration,
                            initial_messages: [
                              {
                                role: 'system',
                                content: e.target.value
                              }
                            ]
                          };
                          handleInputChange('configuration', newConfig);
                        }}
                        placeholder="Enter the system message that defines the agent's behavior"
                        className="w-full px-3 py-2 border rounded-md bg-background min-h-[150px]"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Define the agent's personality and behavior instructions
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.conversation_config?.interruption_enabled ?? true}
                            onChange={(e) => handleConfigChange('conversation_config', 'interruption_enabled', e.target.checked)}
                            className="rounded"
                          />
                          <span className="text-sm font-medium">Allow Interruptions</span>
                        </label>
                        <p className="text-xs text-muted-foreground mt-1">
                          Allow users to interrupt the agent while speaking
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Response Timeout (ms)</label>
                        <Input
                          type="number"
                          value={formData.conversation_config?.response_timeout || 5000}
                          onChange={(e) => handleConfigChange('conversation_config', 'response_timeout', parseInt(e.target.value))}
                          min={1000}
                          max={30000}
                          step={1000}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Advanced Tab */}
            {activeTab === 'advanced' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Webhook URL</label>
                  <Input
                    value={formData.webhook_url || ''}
                    onChange={(e) => handleInputChange('webhook_url', e.target.value)}
                    placeholder="https://your-webhook-url.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Tags (comma-separated)</label>
                  <Input
                    value={formData.tags?.join(', ') || ''}
                    onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()))}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>
              </div>
            )}

            {/* Enhanced Form Actions */}
            <div className="flex justify-end gap-3 pt-6 border-t bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 -mx-6 px-6 py-4 mt-6">
              <Button type="button" variant="outline" onClick={onCancel} className="min-w-[100px]">
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 min-w-[120px]"
              >
                {agent ? 'Update Agent' : 'Create Agent'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
