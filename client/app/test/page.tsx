'use client';

import { useState, useEffect } from 'react';
import { PipecatWidget } from '../../components/PipecatWidget';
import { SimpleVoiceWidget } from '../../components/SimpleVoiceWidget';
import { apiClient, Agent } from '../../lib/api';

export default function TestPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      const agentsData = await apiClient.getAgents();
      setAgents(agentsData);

      // Auto-select first active agent
      const activeAgent = agentsData.find(agent => agent.status === 'active');
      if (activeAgent?.id) {
        setSelectedAgentId(activeAgent.id);
      }
    } catch (err) {
      console.error('Failed to load agents:', err);
      setError('Failed to load agents. Make sure the server is running.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading agents...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-600">{error}</div>
      </div>
    );
  }

  const selectedAgent = agents.find(agent => agent.id === selectedAgentId);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Voice Agent Test</h1>

        {/* Agent Selection */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Select Agent</h2>

          {agents.length === 0 ? (
            <div className="text-gray-600">
              No agents found. Please create an agent first.
            </div>
          ) : (
            <div className="space-y-4">
              <select
                value={selectedAgentId}
                onChange={(e) => setSelectedAgentId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white"
              >
                <option value="">Select an agent...</option>
                {agents.map(agent => (
                  <option key={agent.id} value={agent.id}>
                    {agent.name} ({agent.status})
                  </option>
                ))}
              </select>

              {selectedAgent && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium">{selectedAgent.name}</h3>
                  <p className="text-gray-600 text-sm mt-1">
                    {selectedAgent.description || 'No description'}
                  </p>
                  <div className="mt-2 text-sm text-gray-500">
                    <span>Type: {selectedAgent.type}</span>
                    <span className="ml-4">Status: {selectedAgent.status}</span>
                    <span className="ml-4">STT: {selectedAgent.stt_service || 'default'}</span>
                    <span className="ml-4">LLM: {selectedAgent.llm_service || 'default'}</span>
                    <span className="ml-4">TTS: {selectedAgent.tts_service || 'default'}</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Voice Interface */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Voice Interface</h2>

          {selectedAgentId ? (
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  Testing with agent: <strong>{selectedAgent?.name}</strong>
                </p>
              </div>

              {/* Simple Voice Widget */}
              <div>
                <h3 className="text-lg font-medium mb-3">Simple Voice Widget</h3>
                <SimpleVoiceWidget agentId={selectedAgentId} />
              </div>

              {/* Original Pipecat Widget */}
              <div>
                <h3 className="text-lg font-medium mb-3">Pipecat Widget (Original)</h3>
                <div className="flex justify-center">
                  <PipecatWidget agentId={selectedAgentId} testMode={true} />
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500">
              Please select an agent to start voice conversation
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">Instructions</h2>
          <ul className="text-blue-700 space-y-2">
            <li>• Select an agent from the dropdown above</li>
            <li>• Click the microphone button to start/stop recording</li>
            <li>• Speak clearly and wait for the agent to respond</li>
            <li>• Make sure your browser has microphone permissions</li>
            <li>• Check the browser console for any error messages</li>
          </ul>
        </div>

        {/* Debug Info */}
        <div className="bg-gray-100 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <div className="text-sm text-gray-600 space-y-1">
            <div>Total Agents: {agents.length}</div>
            <div>Selected Agent ID: {selectedAgentId || 'None'}</div>
            <div>API Base URL: {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860'}</div>
            <div>Test Mode: Enabled</div>
          </div>
        </div>
      </div>
    </div>
  );
}
