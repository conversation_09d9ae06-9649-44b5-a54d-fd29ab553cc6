import json
import argparse
import asyncio
from dotenv import load_dotenv
from loguru import logger

from utils.config import AppConfig
from utils.bot_framework import BaseBot
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.audio.vad.vad_analyzer import VADParams
from pipecat.frames.frames import TTSSpeakFrame
# REMOVE: from utils.call_log_service import CallLogService


class SimpleBot(BaseBot):
    def __init__(self, config: AppConfig, agent_config: dict = None, vad_config: VADParams = None, room_url: str = None, app=None):
        super().__init__(config, agent_config, vad_config=vad_config, room_url=room_url)
        self.agent_config = agent_config
        self.context = None
        self.context_aggregator = None

    async def _setup_services_impl(self):
        """Implementation-specific service setup."""
        await super()._setup_services_impl()
        self.services.initialize_services(self.config, self.agent_config)
        logger.debug("Services initialized in SimpleBot.")

        initial_messages = self.agent_config.get("configuration", {}).get(
            "initial_messages",
            [
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Keep responses short and to the point.",
                }
            ],
        ) if self.agent_config else [
            {
                "role": "system",
                "content": "You are a helpful assistant. Keep responses short and to the point.",
            }
        ]
        self.context = OpenAILLMContext(messages=initial_messages)
        logger.debug("OpenAILLMContext created.")

        if not self.services.llm:
            logger.error("LLM service is not initialized.")
            raise RuntimeError("LLM service is not initialized.")
        logger.debug("LLM service is initialized.")

        self.context_aggregator = self.services.llm.create_context_aggregator(self.context)
        if not self.context_aggregator:
            logger.error("Failed to create context aggregator.")
            raise RuntimeError("Failed to create context aggregator.")
        logger.debug(f"Context aggregator created: {self.context_aggregator}")

    async def _create_transport(self, factory, url: str, token: str, vad_config: VADParams = None):
        """Implementation-specific transport creation."""
        return factory.create_simple_assistant_transport(url, token, vad_config)

    async def _create_pipeline_impl(self):
        """Implementation-specific pipeline setup."""
        pass  # Pipeline creation now happens in BaseBot

    async def _handle_first_participant(self):
        """Implementation-specific first participant handling - VOICE GREETING IMPLEMENTATION."""
        logger.debug("Entering _handle_first_participant function - VOICE GREETING.")

        if not self.task:
            logger.error("Pipeline Task is not initialized in _handle_first_participant.")
            return

        try:
            # Get custom starting phrase from conversation config, fallback to default
            default_greeting = "Hello! I'm here to help. What can I do for you?"

            if self.agent_config:
                conversation_config = self.agent_config.get("conversation_config", {})
                initial_greeting = conversation_config.get("starting_phrase", default_greeting)
                if not initial_greeting or initial_greeting.strip() == "":
                    initial_greeting = default_greeting
            else:
                initial_greeting = default_greeting

            logger.debug(f"Queueing TTSSpeakFrame with greeting: '{initial_greeting}'")
            await self.task.queue_frame(TTSSpeakFrame(initial_greeting))
            logger.info("TTSSpeakFrame queued successfully for initial voice greeting.")
            logger.debug("Exiting _handle_first_participant - SimpleBot")
        except Exception as e:
            logger.error(f"Failed to queue TTSSpeakFrame for initial voice greeting: {e}")


async def main(agent_config={}):
    """Setup and run the simple agent."""
    from utils.run_helpers import run_bot

    parser = argparse.ArgumentParser(description="Run the SimpleBot.")
    parser.add_argument(
        "-u", "--url", type=str, required=True, help="Daily room URL"
    )
    parser.add_argument(
        "-t", "--token", type=str, required=True, help="Daily room token"
    )
    parser.add_argument(
        "--stt-service", type=str, help="STT service to use"
    )
    parser.add_argument(
        "--llm-service", type=str, help="LLM service to use"
    )
    parser.add_argument(
        "--tts-service", type=str, help="TTS service to use"
    )
    parser.add_argument(
        "--vad-config", type=str, help="VAD configuration as a JSON string"
    )
    args, unknown = parser.parse_known_args()

    vad_config = None
    if args.vad_config:
        try:
            vad_config_dict = json.loads(args.vad_config)
            vad_config = VADParams(**vad_config_dict)
            logger.debug(f"Loaded VAD config from JSON: {vad_config_dict}")
            logger.debug(f"VADParams object created: {vad_config}")
        except json.JSONDecodeError:
            print("Error: Invalid JSON format in VAD configuration.")
            return

    config = AppConfig()
    bot = SimpleBot( # REMOVE call_log_service
        config,
        agent_config=agent_config,
        vad_config=vad_config,
    )
    await bot.setup_services()
    await bot.setup_transport(args.url, args.token)
    bot.create_pipeline()  # create_pipeline is now called after setup_transport
    await bot.start()

if __name__ == "__main__":
    import sys
    if "-c" in sys.argv:
        agent_config = json.loads(sys.argv[sys.argv.index("-c") + 1])
        asyncio.run(main(agent_config))
    else:
        asyncio.run(main())