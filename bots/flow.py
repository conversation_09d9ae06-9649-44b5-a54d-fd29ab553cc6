"""Flow-based bot implementation."""

import asyncio
import json
from dotenv import load_dotenv
import argparse

from utils.calcom_api import CalComAPI, BookingDetails
from utils.config import AppConfig
from utils.bot_framework import BaseBot
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat_flows import FlowManager, FlowArgs, FlowConfig, FlowResult
from pipecat.audio.vad.vad_analyzer import VADParams
from utils.call_log_service import CallLogService
from loguru import logger
# REMOVE: from utils.call_log_service import CallLogService

# Load environment variables from .env file
load_dotenv()

# Initialize Cal.com API
calcom_api = CalComAPI()

# === 1. Function Handler Registry ===
FUNCTION_HANDLERS = {
    "handle_availability_check": lambda args: handle_availability_check(args),
    "handle_time_slot_selection": lambda args: handle_time_slot_selection(args),
    "handle_booking_confirmation": lambda args: handle_booking_confirmation(args),
    # Add other function handlers here if you define more in flow.py
}

async def handle_availability_check(args: FlowArgs) -> FlowResult:
    """Check availability and present options to the user."""
    logger.debug("handle_availability_check function called.")
    availability = await calcom_api.get_availability(days=7)

    if not availability["success"]:
        availability = await calcom_api.get_availability(days=7)
        if not availability["success"]:
            return FlowResult(
                status="error",
                message="I sincerely apologize.",
                data={"availability_check_failed": True},
            )

    formatted = calcom_api._last_availability_check
    if not formatted or not formatted["dates"]:
        return FlowResult(
            status="error",
            message="I apologize",
            data={"no_availability": True},
        )

    available_dates = formatted["dates"][:2]
    date_options = " or ".join(available_dates)

    return FlowResult(
        status="success",
        message=f"I see we have availability on {date_options}. Which day would work better for you?",
        data={"available_dates": available_dates},
    )

async def handle_time_slot_selection(args: FlowArgs) -> FlowResult:
    """Present time slots for the selected date."""
    selected_date = args.get("selected_date")

    if not selected_date:
        return FlowResult(
            status="error",
            message="I apologize, but I didn't catch which date you preferred. Could you please let me know if you'd prefer {available_dates}?",
            data={"missing_date": True},
        )

    morning_slot, afternoon_slot = calcom_api.get_morning_afternoon_slots(selected_date)

    if not morning_slot and not afternoon_slot:
        return FlowResult(
            status="error",
            message="I apologize, but it seems those time slots are no longer available. Let me check availability again.",
            data={"retry_availability": True},
        )

    time_options = []
    if morning_slot:
        time_options.append(morning_slot["time"])
    if afternoon_slot:
        time_options.append(afternoon_slot["time"])

    time_options_str = " or ".join(time_options)
    return FlowResult(
        status="success",
        message=f"Great. On {selected_date}, I have slots at {time_options_str}. Which would you prefer?",
        data={"morning_slot": morning_slot, "afternoon_slot": afternoon_slot},
    )

async def handle_booking_confirmation(args: FlowArgs) -> FlowResult:
    """Attempt to book the selected time slot."""
    selected_slot = args.get("selected_slot")

    if not selected_slot:
        return FlowResult(
            status="error",
            message="I apologize, but I didn't catch which time slot you preferred. Could you please let me know which time works better for you?",
            data={"missing_time": True},
        )

    booking_details: BookingDetails = {
        "name": args.get("name", "Unknown"),
        "email": "<EMAIL>",  # In production, get from args
        "company": args.get("company", "Unknown"),
        "phone": "************",  # In production, get from args
        "timezone": "UTC",
        "startTime": selected_slot["datetime"],
        "notes": "Booking from AI Lead Qualifier",
    }

    # First booking attempt
    booking = await calcom_api.create_booking(booking_details)
    if not booking["success"]:
        # Second booking attempt
        booking = await calcom_api.create_booking(booking_details)
        if not booking["success"]:
            return FlowResult(
                status="error",
                message="I sincerely apologize, but our booking system seems to be having issues right now. To make sure you get this time slot, I can have our scheduling team call you back within the next 30 minutes to confirm it. Alternatively, you can book directly through our scheduling line at 555-0123. Which would you prefer?",
                data={"booking_failed": True},
            )

    return FlowResult(
        status="success",
        message=f"Excellent! I've confirmed your demo for {selected_slot['date']} at {selected_slot['time']}. You'll receive a calendar invitation shortly with all the details. Is there anything else you'd like to know about the demo?",
        data={"booking": booking["booking"]},
    )
class FlowBot(BaseBot):
    def __init__(self, config: AppConfig, agent_config: dict = None, vad_config: VADParams = None, call_log_service: CallLogService = None, room_url: str = None, app=None):
        super().__init__(config, agent_config, vad_config=vad_config, call_log_service=call_log_service, room_url=room_url)  # Corrected super() call and added app
        self.flow_config: FlowConfig = None
        self.agent_config = agent_config
        self.flow_manager = None

    async def _setup_services_impl(self):
        """Implementation-specific service setup."""
        await super()._setup_services_impl()
        self.services.initialize_services(self.config, self.agent_config)
        if self.agent_config:
            self.flow_config = self.agent_config.get("configuration", {})
        else:
            # Provide a default flow configuration if none is provided in agent_config
            self.flow_config = {
                "nodes": {
                    "rapport_building": {
                        "role_messages": [
                            {"role": "system", "content": "You are a helpful assistant."},
                            {"role": "user", "content": "Hi there!"}
                        ],
                        "transition_to": "check_availability"
                    },
                    "check_availability": {
                        "functions": [
                            {
                                "type": "function",
                                "function":{
                                    "name": "handle_availability_check",
                                    "handler": "handle_availability_check",
                                    "parameters": {"type": "object", "properties": {}},
                                    "description": "Check calendar availability",
                                    "transition_to": "present_time_slots",
                                }
                            }
                        ],
                        "transition_to": "present_time_slots"  # Default transition
                    },
                    "present_time_slots": {
                        "functions": [
                            {
                                "type": "function",
                                "function":{
                                "name": "handle_time_slot_selection",
                                "handler": "handle_time_slot_selection",
                                "parameters": {"type": "object", "properties": {}},
                                "description": "Present time slots for the selected date",
                                "transition_to": "confirm_booking"
                            }}
                        ],
                        "transition_to": "confirm_booking"  # Default transition
                    },
                    "confirm_booking": {
                        "functions": [
                            {
                                "type": "function",
                                "function":{
                                "name": "handle_booking_confirmation",
                                "handler": "handle_booking_confirmation",
                                "parameters": {"type": "object", "properties": {}},
                                "description": "Attempt to book the selected time slot",
                            }}
                        ]
                    }
                }
            }

        await self._resolve_function_handlers()

        initial_messages = self.flow_config["nodes"]["rapport_building"][
            "role_messages"
        ]
        self.context = OpenAILLMContext(messages=initial_messages)
        self.context_aggregator = self.services.llm.create_context_aggregator(
            self.context
        )

    async def _resolve_function_handlers(self):
        """Resolves function handler strings in flow_config with actual function objects."""
        logger.debug("Resolving function handlers in flow_config...")
        for node_name, node_config in self.flow_config["nodes"].items():
            if "functions" in node_config:
                for function_def in node_config["functions"]:
                    handler_name = function_def["function"].get("handler")
                    if handler_name:
                        handler_function = FUNCTION_HANDLERS.get(handler_name)
                        if handler_function:
                            function_def["function"]["handler"] = handler_function
                            logger.debug(f"Resolved handler '{handler_name}' in node '{node_name}'.")
                        else:
                            logger.warning(f"Handler '{handler_name}' not found in FUNCTION_HANDLERS registry (node: '{node_name}').")


    async def _create_transport(self, factory, url: str, token: str, vad_config: VADParams = None):
        """Implementation-specific transport creation."""
        return factory.create_flow_assistant_transport(url, token, vad_config)

    async def _handle_first_participant(self):
        """Implementation-specific first participant handling."""
        logger.debug("Entering _handle_first_participant - FlowBot")
        await self.flow_manager.initialize()
        logger.debug("Exiting _handle_first_participant - FlowBot")


    async def _create_pipeline_impl(self):
        """Implementation-specific pipeline setup (FlowBot specific)."""
        self.flow_manager = FlowManager(
            task=self.task,
            llm=self.services.llm,
            context_aggregator=self.pipeline_builder.context_aggregator,
            tts=self.services.tts,
            flow_config=self.flow_config,
        )

async def main(agent_config={}):
    """Setup and run the lead qualification agent."""
    from utils.run_helpers import run_bot

    parser = argparse.ArgumentParser(description="Run the FlowBot.")
    parser.add_argument(
        "-u", "--url", type=str, required=True, help="Daily room URL"
    )
    parser.add_argument(
        "-t", "--token", type=str, required=True, help="Daily room token"
    )
    parser.add_argument(
        "--stt-service", type=str, help="STT service to use"
    )
    parser.add_argument(
        "--llm-service", type=str, help="LLM service to use"
    )
    parser.add_argument(
        "--tts-service", type=str, help="TTS service to use"
    )
    parser.add_argument(
        "--vad-config", type=str, help="VAD configuration as a JSON string"
    )
    args, unknown = parser.parse_known_args()

    vad_config = None
    if args.vad_config:
        try:
            vad_config_dict = json.loads(args.vad_config)
            vad_config = VADParams(
                start_secs=vad_config_dict.get("start_secs", 0.3),
                stop_secs=vad_config_dict.get("stop_secs", 0.3),
                threshold=vad_config_dict.get("threshold", 0.6),
                min_speech_duration_ms=vad_config_dict.get("min_speech_duration_ms", 300),
                min_silence_duration_ms=vad_config_dict.get("min_silence_duration_ms", 150)
            )
        except json.JSONDecodeError:
            print("Error: Invalid JSON format in VAD configuration.")
            return

    config = AppConfig()
    bot = FlowBot( # REMOVE call_log_service
        config,
        agent_config=agent_config,
        vad_config=vad_config,
    )
    await bot.setup_services()
    await bot.setup_transport(args.url, args.token)
    bot.create_pipeline()  # create_pipeline is now called after setup_transport
    await bot.start()

if __name__ == "__main__":
    import sys
    if "-c" in sys.argv:
        c_index = sys.argv.index("-c")
        agent_config_str = sys.argv[c_index + 1]
        try:
            agent_config = json.loads(agent_config_str)
        except json.JSONDecodeError:
            print("Error: Invalid JSON format in agent configuration.")
            sys.exit(1)
        asyncio.run(main(agent_config))
    else:
        asyncio.run(main())