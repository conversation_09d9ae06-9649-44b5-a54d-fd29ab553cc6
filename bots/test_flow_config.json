{"nodes": {"close_call": {"functions": [], "post_actions": [{"type": "end_conversation"}], "task_messages": [{"role": "system", "content": "Thank them warmly and end the conversation professionally. If there were any booking issues, make sure to clearly state the next steps."}]}, "confirm_booking": {"functions": [{"type": "function", "function": {"name": "handle_booking_confirmation", "handler": "handle_booking_confirmation", "parameters": {"type": "object", "required": ["selected_slot"], "properties": {"selected_slot": {"type": "object", "required": ["date", "time", "datetime"], "properties": {"date": {"type": "string"}, "time": {"type": "string"}, "datetime": {"type": "string"}, "is_morning": {"type": "boolean"}}, "description": "The time slot selected by the user"}}}, "description": "Confirm and create the booking", "transition_to": "close_call"}}], "task_messages": [{"role": "system", "content": "Attempt to book the selected time slot. If successful, confirm the booking enthusiastically. If it fails, handle gracefully with alternative booking options."}]}, "rapport_building": {"functions": [{"type": "function", "function": {"name": "collect_name", "parameters": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "The caller's name"}}}, "description": "Record the caller's name", "transition_to": "check_availability"}}], "role_messages": [{"role": "system", "content": "You are a lead qualification agent. Your responses will be converted to audio. Keep responses natural and friendly."}], "task_messages": [{"role": "system", "content": "Greet the caller warmly and ask for their name."}]}, "check_availability": {"functions": [{"type": "function", "function": {"name": "handle_availability_check", "handler": "handle_availability_check", "parameters": {"type": "object", "properties": {}}, "description": "Check calendar availability", "transition_to": "present_time_slots"}}], "task_messages": [{"role": "system", "content": "Use assumptive closing technique to naturally transition to booking a demo. Say something like 'Let's get you set up with a quick demo so you can see firsthand how this will help your business. Let me check our calendar...'"}]}, "present_time_slots": {"functions": [{"type": "function", "function": {"name": "handle_time_slot_selection", "handler": "handle_time_slot_selection", "parameters": {"type": "object", "required": ["selected_date"], "properties": {"selected_date": {"type": "string", "description": "The date selected by the user"}}}, "description": "Present time slots for selected date", "transition_to": "confirm_booking"}}], "task_messages": [{"role": "system", "content": "Based on the user's date preference, present available time slots. If they haven't chosen a date yet, ask them to choose from the available dates first."}]}}, "initial_node": "rapport_building"}