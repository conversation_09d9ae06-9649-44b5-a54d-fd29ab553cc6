#!/usr/bin/env python3
"""
Comprehensive test script to verify voice connection functionality.
"""

import requests
import json
import sys
import time
import asyncio
from typing import Dict, Any

API_BASE_URL = "http://localhost:7860"

def test_server_health():
    """Test that the server is running and healthy."""
    print("🔍 Testing server health...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print("   Make sure the server is running with: python server.py")
        return False

def get_test_agent():
    """Get an active agent for testing."""
    print("🔍 Getting test agent...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            active_agents = [a for a in agents if a.get('status') == 'active']
            
            if active_agents:
                agent = active_agents[0]
                print(f"✅ Found test agent: {agent['name']} (ID: {agent['id']})")
                return agent
            else:
                print("⚠️  No active agents found")
                return None
        else:
            print(f"❌ Failed to get agents: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Failed to get agents: {e}")
        return None

def test_connect_endpoint(agent_id: str):
    """Test the /connect endpoint with a valid agent ID."""
    print(f"🔍 Testing /connect endpoint with agent ID: {agent_id}")
    
    # Test 1: Query parameter format
    print("\n1. Testing with query parameter:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/connect?agent_id={agent_id}",
            timeout=15
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'room_url' in data and 'token' in data:
                print("   ✅ Successfully created room and token")
                print(f"   Room URL: {data['room_url'][:50]}...")
                print(f"   Token: {data['token'][:20]}...")
                return True
            else:
                print(f"   ❌ Missing room_url or token in response")
                print(f"   Response: {data}")
                return False
        else:
            print(f"   ❌ Connection failed with status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
        return False

def test_connect_request_body(agent_id: str):
    """Test the /connect endpoint with request body format (RTVI client format)."""
    print(f"\n2. Testing with request body (RTVI format):")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/connect",
            json={"agent_id": agent_id},
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'room_url' in data and 'token' in data:
                print("   ✅ Successfully created room and token via request body")
                print(f"   Room URL: {data['room_url'][:50]}...")
                print(f"   Token: {data['token'][:20]}...")
                return True
            else:
                print(f"   ❌ Missing room_url or token in response")
                print(f"   Response: {data}")
                return False
        else:
            print(f"   ❌ Connection failed with status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
        return False

def test_direct_url(agent_id: str):
    """Test the direct URL access."""
    print(f"\n3. Testing direct URL access:")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/?agent_id={agent_id}",
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Direct URL access working")
            print(f"   URL: {API_BASE_URL}/?agent_id={agent_id}")
            return True
        else:
            print(f"   ❌ Direct URL failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Direct URL test failed: {e}")
        return False

def test_templates():
    """Test the templates endpoint."""
    print("🔍 Testing templates endpoint...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/templates/", timeout=10)
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ Found {len(templates)} templates")
            
            # Test creating agent from template
            if templates:
                template = templates[0]
                print(f"   Testing template: {template['name']}")
                
                create_response = requests.post(
                    f"{API_BASE_URL}/agents/from-template/{template['id']}?name=Test Agent from Template",
                    timeout=10
                )
                
                if create_response.status_code == 201:
                    agent_data = create_response.json()
                    agent_id = agent_data.get('id')
                    print(f"   ✅ Created agent from template: {agent_id}")
                    
                    # Clean up
                    delete_response = requests.delete(f"{API_BASE_URL}/agents/{agent_id}")
                    if delete_response.status_code == 200:
                        print("   ✅ Cleaned up test agent")
                    
                    return True
                else:
                    print(f"   ❌ Failed to create agent from template: {create_response.status_code}")
                    return False
            return True
        else:
            print(f"❌ Failed to get templates: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Templates test failed: {e}")
        return False

def create_test_agent_if_needed():
    """Create a test agent if none exist."""
    print("🔍 Checking for test agents...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            active_agents = [a for a in agents if a.get('status') == 'active']
            
            if active_agents:
                print(f"✅ Found {len(active_agents)} active agents")
                return active_agents[0]
            else:
                print("⚠️  No active agents found. Creating test agent...")
                
                test_agent = {
                    "name": "Voice Test Agent",
                    "description": "Test agent for voice connection testing",
                    "type": "simple",
                    "status": "active",
                    "category": "test",
                    "stt_service": "deepgram",
                    "llm_service": "openai",
                    "tts_service": "elevenlabs",
                    "configuration": {
                        "initial_messages": [
                            {
                                "role": "system",
                                "content": "You are a test assistant. Keep responses very short and confirm you can hear the user."
                            }
                        ]
                    }
                }
                
                create_response = requests.post(
                    f"{API_BASE_URL}/agents/",
                    json=test_agent,
                    timeout=10
                )
                
                if create_response.status_code == 201:
                    agent_data = create_response.json()
                    print(f"✅ Created test agent: {agent_data['name']} (ID: {agent_data['id']})")
                    return agent_data
                else:
                    print(f"❌ Failed to create test agent: {create_response.status_code}")
                    return None
        else:
            print(f"❌ Failed to check agents: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Failed to check/create agents: {e}")
        return None

def main():
    """Run comprehensive voice connection tests."""
    print("🚀 VoxDiscover Voice Connection Test")
    print("=" * 60)
    
    # Test server health
    if not test_server_health():
        return False
    
    # Get or create test agent
    agent = create_test_agent_if_needed()
    if not agent:
        print("❌ Cannot proceed without a test agent")
        return False
    
    agent_id = agent['id']
    agent_name = agent['name']
    
    print(f"\n🎯 Testing with agent: {agent_name} (ID: {agent_id})")
    print("-" * 60)
    
    # Run connection tests
    tests = [
        ("Connect Endpoint (Query)", lambda: test_connect_endpoint(agent_id)),
        ("Connect Endpoint (Body)", lambda: test_connect_request_body(agent_id)),
        ("Direct URL Access", lambda: test_direct_url(agent_id)),
        ("Templates System", test_templates),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Voice connection system is working correctly.")
        print("\n🚀 Next steps:")
        print("1. Start the frontend: cd client && npm run dev")
        print("2. Open http://localhost:3000/test in your browser")
        print("3. Select the test agent and click 'Connect' to test voice")
        print(f"4. Or test directly: {API_BASE_URL}/?agent_id={agent_id}")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("   Common issues:")
        print("   - Missing API keys in .env file")
        print("   - Supabase connection problems")
        print("   - Daily.co API key issues")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
