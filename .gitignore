# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
.venv/
.env
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/

# IDE - VSCode
.vscode/
*.code-workspace

# IDE - PyCharm
.idea/
*.iml
*.iws
.idea_modules/

# IDE - Jupyter Notebook
.ipynb_checkpoints

# System files
.DS_Store
Thumbs.db

# Logs and databases
*.log
*.sqlite
*.db

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# pytest
.pytest_cache/

# Environment directories
.python-version
