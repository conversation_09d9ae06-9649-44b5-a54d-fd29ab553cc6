#!/bin/bash

# VoxDiscover Voice AI Agent Management System Setup Script

echo "🚀 Setting up VoxDiscover Voice AI Agent Management System..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3.10+ and try again."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed. Please install Node.js 18+ and try again."
    exit 1
fi

echo "✅ Python and Node.js found"

# Setup Backend
echo "📦 Setting up backend..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

echo "✅ Backend setup complete"

# Setup Frontend
echo "📦 Setting up frontend..."
cd client

# Check if package manager is available
if command -v pnpm &> /dev/null; then
    echo "Installing frontend dependencies with pnpm..."
    pnpm install
elif command -v npm &> /dev/null; then
    echo "Installing frontend dependencies with npm..."
    npm install
else
    echo "❌ No package manager found. Please install npm or pnpm and try again."
    exit 1
fi

# Create environment file if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "Creating frontend environment file..."
    cp .env.local.example .env.local
    echo "📝 Please edit client/.env.local with your configuration"
fi

cd ..

echo "✅ Frontend setup complete"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  Backend .env file not found. Please make sure you have a .env file with your API keys."
    echo "   Required variables:"
    echo "   - DAILY_API_KEY"
    echo "   - SUPABASE_URL"
    echo "   - SUPABASE_KEY"
    echo "   - OPENAI_API_KEY (or other LLM service keys)"
    echo "   - DEEPGRAM_API_KEY (or other STT service keys)"
    echo "   - ELEVENLABS_API_KEY (or other TTS service keys)"
else
    echo "✅ Backend .env file found"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start the system:"
echo "1. Start the backend:"
echo "   source venv/bin/activate"
echo "   python server.py"
echo ""
echo "2. In a new terminal, start the frontend:"
echo "   cd client"
echo "   npm run dev  # or pnpm dev"
echo ""
echo "3. Open http://localhost:3000 in your browser"
echo ""
echo "📚 For more information, see the README.md file"
