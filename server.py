import argparse
import asyncio
import os
import sys
import time
import json
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional, List

import aiohttp
from fastapi import FastAPI, HTTPException, Request, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from loguru import logger
from pydantic import BaseModel, Field
from supabase import create_client, Client

from utils.custom_daily_helpers import CustomDailyRESTHelper
from utils.config import AppConfig
from utils.agent_service import AgentService
from utils.agent_model import Agent, AgentTemplate, AgentTestSession, ServiceCapability, ServiceConfigurationTemplate
from utils.agent_templates import AgentTemplateService
from utils.call_log_service import CallLogService
from utils.call_log_model import CallLogEntry
from utils.service_capabilities import get_service_capability, get_services_by_type, get_configuration_templates
import subprocess  # Add this import at the top


from pipecat.transports.services.helpers.daily_rest import (
    DailyRESTHelper,
    DailyRoomParams,
)

# Configuration (Keep as is)
default_host = os.getenv("HOST", "0.0.0.0")
default_port = int(os.getenv("FAST_API_PORT", "7860"))
MAX_BOTS_PER_ROOM = 1
ROOM_TIMEOUT = 60
CLEANUP_INTERVAL = 60  # Check every 60 seconds

active_rooms = {}  # {room_url: {"pid": int, "created_at": float, "last_checked": float}}
bot_procs = {}  # {pid: subprocess.Popen}
daily_helpers = {}  # Daily API helpers



# Argument parsing (Keep as is)
parser = argparse.ArgumentParser(description="Bot FastAPI server")
parser.add_argument("--host", type=str, default=default_host, help="Host address")
parser.add_argument("--port", type=int, default=default_port, help="Port number")
parser.add_argument("--reload", action="store_true", help="Reload code on change")
parser.add_argument(
    "--bot-type",
    type=str,
    choices=["simple", "flow"],
    default="simple",
    help="Type of bot to run (simple or flow)",
)
args = parser.parse_args()

# Initialize configuration (Keep as is)
os.environ["BOT_TYPE"] = args.bot_type
config = AppConfig()

# Configure logging (Keep as is)
logger.remove()
logger.add(
    sys.stderr,
    level="DEBUG",
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    enqueue=True,
    backtrace=True,
    diagnose=True,
)

async def delete_room(room_url: str):
    """Safely delete a Daily room and clean up resources"""
    if room_url in active_rooms:
        logger.warning(f"Room {room_url} in active rooms, skipping deletion.")
        return

    try:
            room_data = await daily_helpers["rest"].daily_rest_helper.get_room_from_url(room_url)
            if room_data: # Only attempt delete if room exists
                await daily_helpers["rest"].delete_room_by_url(room_url)
                logger.success(f"Deleted Daily room {room_url}")
            else:
                logger.warning(f"Room {room_url} not found on Daily.co, skipping API deletion.")
    except Exception as e:
            logger.warning(f"Error checking room existence before deletion {room_url}: {e}")


async def room_cleanup_task():
    """Periodic room cleanup and maintenance"""

    try:
        all_rooms = await daily_helpers["rest"].get_rooms()
        logger.debug(f"Fetched {len(all_rooms)} rooms from Daily API.")
    except Exception as e:
        logger.error(f"Error fetching rooms from Daily API during cleanup: {e}")
        all_rooms = []

    active_room_urls = {room.url for room in all_rooms if room.url}
    logger.debug(f"Active room URLs from Daily API: {active_room_urls}")

    rooms_to_delete = []
    for room_url in active_room_urls:
        try:
            participants = await daily_helpers["rest"].get_participants(room_url)
            if not participants:
                logger.info(f"Room {room_url} has no participants. Marking for immediate deletion")
                rooms_to_delete.append(room_url)
            else:
                logger.debug(f"Room {room_url} has active participants..")
        except Exception as e:
            logger.error(f"Error checking or processing room {room_url}: {e}")
            logger.info(f"Room {room_url} error during processing. Marking for immediate deletion due to error.")
            rooms_to_delete.append(room_url)

    for room_url in rooms_to_delete:
            await delete_room(room_url)
            logger.info(f"Room {room_url} deleted and removed from active_rooms.")

    logger.debug("Room cleanup cycle completed.")



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage server lifecycle, initialize resources, and cleanup."""
    logger.info("Starting lifespan event - initializing resources (1)")
    aiohttp_session = aiohttp.ClientSession()
    daily_rest_helper = DailyRESTHelper(
        daily_api_key=config.daily["api_key"],
        daily_api_url=config.daily["api_url"],
        aiohttp_session=aiohttp_session,
    )
    daily_helpers["rest"] = CustomDailyRESTHelper(daily_rest_helper)

    agent_service = AgentService(supabase)
    call_log_service = CallLogService(supabase)

    app.state.daily_helpers = daily_helpers
    app.state.agent_service = agent_service
    app.state.call_log_service = call_log_service
    app.state.bot_procs = bot_procs

    cleanup_task = asyncio.create_task(room_cleanup_task())
    app.state.cleanup_task = cleanup_task # Store task in app state to manage lifecycle

    try:
         yield
    finally:
        logger.info("Stopping lifespan event - cleaning up resources")
        # Cleanup resources
        logger.info("Cancelling cleanup task...")
        cleanup_task.cancel()
        try:
            await cleanup_task # Wait for cancellation to complete
        except asyncio.CancelledError:
            logger.info("Cleanup task cancelled successfully.")
        except Exception as e:
            logger.error(f"Error during cleanup task cancellation: {e}")

        logger.info("Cleaning up active rooms...")
        # Cleanup all remaining rooms
        for room_url in list(active_rooms.keys()):
            logger.info(f"Cleaning up room {room_url} during shutdown.")
            await delete_room(room_url)
        active_rooms.clear() # Clear the active rooms dictionary

        logger.info("Closing aiohttp session...")
        await aiohttp_session.close()
        logger.info("Lifespan event finished - server cleanup complete.")


# Initialize FastAPI app (Keep as is)
app = FastAPI(lifespan=lifespan)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Supabase Setup (Keep as is) ---
url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(url, key)

# --- Dependency Injection (Keep as is) ---
def get_daily_helpers(request: Request):
    return request.app.state.daily_helpers

def get_agent_service(request: Request):
    return request.app.state.agent_service

def get_call_log_service(request: Request):
    return request.app.state.call_log_service

# --- API Endpoints for Agent Management (Keep as is) ---

@app.post("/agents/", response_model=Agent, status_code=201)
async def create_agent_endpoint(
    agent: Agent, agent_service: AgentService = Depends(get_agent_service)
):
    """Create a new AI agent."""
    return await agent_service.create_agent(agent)

@app.get("/agents/", response_model=List[Agent])
async def get_all_agents_endpoint(
    agent_service: AgentService = Depends(get_agent_service)
):
    """Get all AI agents."""
    return await agent_service.get_all_agents()

@app.get("/agents/{agent_id}", response_model=Agent)
async def get_agent_endpoint(
    agent_id: str, agent_service: AgentService = Depends(get_agent_service)
):
    """Get an AI agent by ID."""
    return await agent_service.get_agent(agent_id)

@app.put("/agents/{agent_id}", response_model=Agent)
async def update_agent_endpoint(
    agent_id: str, agent: Agent, agent_service: AgentService = Depends(get_agent_service)
):
    """Update an AI agent."""
    return await agent_service.update_agent(agent_id, agent)

@app.delete("/agents/{agent_id}")
async def delete_agent_endpoint(
    agent_id: str, agent_service: AgentService = Depends(get_agent_service)
):
    """Delete an AI agent."""
    await agent_service.delete_agent(agent_id)
    return {"message": "Agent deleted"}

# --- Agent Template Endpoints ---

@app.get("/templates/", response_model=List[AgentTemplate])
async def get_agent_templates():
    """Get all available agent templates."""
    return AgentTemplateService.get_all_templates()

@app.get("/templates/{template_id}", response_model=AgentTemplate)
async def get_agent_template(template_id: str):
    """Get a specific agent template by ID."""
    try:
        return AgentTemplateService.get_template_by_id(template_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.get("/templates/category/{category}", response_model=List[AgentTemplate])
async def get_templates_by_category(category: str):
    """Get agent templates by category."""
    return AgentTemplateService.get_templates_by_category(category)

@app.get("/template-categories/")
async def get_template_categories():
    """Get all available template categories."""
    return {"categories": AgentTemplateService.get_categories()}

@app.post("/agents/from-template/{template_id}", response_model=Agent, status_code=201)
async def create_agent_from_template(
    template_id: str,
    name: str,
    description: str = None,
    agent_service: AgentService = Depends(get_agent_service)
):
    """Create a new agent from a template."""
    try:
        agent_data = AgentTemplateService.create_agent_from_template(template_id, name, description)
        agent = Agent(**agent_data)
        return await agent_service.create_agent(agent)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

# --- Agent Testing Endpoints ---

@app.post("/agents/{agent_id}/test")
async def create_test_session(
    agent_id: str,
    daily_helpers = Depends(get_daily_helpers),
    agent_service: AgentService = Depends(get_agent_service)
):
    """Create a test session for an agent."""
    try:
        # Verify agent exists
        agent_data = await agent_service.get_agent(agent_id)

        # Create a Daily room for testing
        room, token = await _create_room_and_token(daily_helpers)

        # Create test session record
        test_session = AgentTestSession(
            agent_id=agent_id,
            room_url=room.url,
            token=token,
            status="active"
        )

        return {
            "session_id": f"test_{agent_id}_{int(time.time())}",
            "room_url": room.url,
            "token": token,
            "agent": agent_data,
            "test_url": f"{room.url}?agent_id={agent_id}"
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error creating test session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create test session: {str(e)}")

@app.get("/agents/{agent_id}/analytics")
async def get_agent_analytics(
    agent_id: str,
    days: int = 30,
    agent_service: AgentService = Depends(get_agent_service),
    call_log_service: CallLogService = Depends(get_call_log_service)
):
    """Get analytics for an agent."""
    try:
        # Verify agent exists
        await agent_service.get_agent(agent_id)

        # Get call logs for the agent (this would need to be implemented in call_log_service)
        # For now, return mock data
        return {
            "agent_id": agent_id,
            "period_days": days,
            "total_calls": 0,
            "total_duration": 0,
            "average_duration": 0.0,
            "success_rate": 0.0,
            "daily_stats": []
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error getting agent analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")

# --- Service Configuration Endpoints ---

@app.get("/services/stt")
async def get_stt_services():
    """Get all available STT services with their capabilities."""
    try:
        services = get_services_by_type("stt")
        return {"services": services}
    except Exception as e:
        logger.error(f"Error getting STT services: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get STT services: {str(e)}")

@app.get("/services/llm")
async def get_llm_services():
    """Get all available LLM services with their capabilities."""
    try:
        services = get_services_by_type("llm")
        return {"services": services}
    except Exception as e:
        logger.error(f"Error getting LLM services: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get LLM services: {str(e)}")

@app.get("/services/tts")
async def get_tts_services():
    """Get all available TTS services with their capabilities."""
    try:
        services = get_services_by_type("tts")
        return {"services": services}
    except Exception as e:
        logger.error(f"Error getting TTS services: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get TTS services: {str(e)}")

@app.get("/services/{service_type}/{service_name}/config")
async def get_service_config_schema(service_type: str, service_name: str):
    """Get configuration schema for a specific service."""
    try:
        capability = get_service_capability(service_type, service_name)
        if not capability:
            raise HTTPException(status_code=404, detail=f"Service {service_name} not found for type {service_type}")

        return {
            "service": capability,
            "config_schema": capability.config_schema
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error getting service config schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get service config: {str(e)}")

@app.get("/services/templates")
async def get_service_templates(
    service_type: Optional[str] = Query(None),
    service_name: Optional[str] = Query(None),
    use_case: Optional[str] = Query(None)
):
    """Get service configuration templates with optional filtering."""
    try:
        templates = get_configuration_templates(service_type, service_name, use_case)
        return {"templates": templates}
    except Exception as e:
        logger.error(f"Error getting service templates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get service templates: {str(e)}")

@app.post("/agents/validate-config")
async def validate_agent_config(agent: Agent):
    """Validate agent configuration against service schemas."""
    try:
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        # Validate STT configuration
        if agent.stt_service and agent.stt_config:
            stt_capability = get_service_capability("stt", agent.stt_service)
            if not stt_capability:
                validation_results["errors"].append(f"Unknown STT service: {agent.stt_service}")
                validation_results["valid"] = False

        # Validate LLM configuration
        if agent.llm_service and agent.llm_config:
            llm_capability = get_service_capability("llm", agent.llm_service)
            if not llm_capability:
                validation_results["errors"].append(f"Unknown LLM service: {agent.llm_service}")
                validation_results["valid"] = False

        # Validate TTS configuration
        if agent.tts_service and agent.tts_config:
            tts_capability = get_service_capability("tts", agent.tts_service)
            if not tts_capability:
                validation_results["errors"].append(f"Unknown TTS service: {agent.tts_service}")
                validation_results["valid"] = False

        return validation_results
    except Exception as e:
        logger.error(f"Error validating agent config: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate config: {str(e)}")

# ================================================================================================
async def _start_bot_process(room_url: str,
                             token: str,
                             agent_config: Dict[str, Any],
                             bot_module: str, stt_service: Optional[str],
                             stt_config: Optional[Any],
                             llm_service: Optional[str],
                             llm_config: Optional[Any],
                             tts_service: Optional[str],
                             tts_config: Optional[Any],
                             vad_config: Optional[Any]):
    """Helper function to start the bot subprocess."""
    env = os.environ.copy()

    # Helper function to serialize config objects
    def serialize_config(config):
        if config is None:
            return None
        if hasattr(config, 'model_dump'):
            return config.model_dump()
        elif hasattr(config, 'dict'):
            return config.dict()
        elif isinstance(config, dict):
            return config
        else:
            return {}

    if stt_service:
        env["STT_SERVICE"] = stt_service
    if stt_config:
        env["STT_CONFIG"] = json.dumps(serialize_config(stt_config))
    if llm_service:
        env["LLM_SERVICE"] = llm_service
    if llm_config:
        env["LLM_CONFIG"] = json.dumps(serialize_config(llm_config))
    if tts_service:
        env["TTS_SERVICE"] = tts_service
    if tts_config:
        env["TTS_CONFIG"] = json.dumps(serialize_config(tts_config))
    if vad_config:
        env["VAD_CONFIG"] = json.dumps(serialize_config(vad_config))

    # Pass Supabase credentials as environment variables
    env["SUPABASE_URL"] = os.environ.get("SUPABASE_URL")
    env["SUPABASE_KEY"] = os.environ.get("SUPABASE_KEY")


    process_args = [
        sys.executable,
        "-m",
        bot_module,
        "-u",
        room_url,
        "-t",
        token,
        "-c",
        json.dumps(agent_config),  # Pass the WHOLE agent data
    ]

    if stt_service:
        process_args.extend(["--stt-service", stt_service])
    if llm_service:
        process_args.extend(["--llm-service", llm_service])
    if tts_service:
        process_args.extend(["--tts-service", tts_service])
    if vad_config:
        process_args.extend(["--vad-config", json.dumps(vad_config)])

    proc = await asyncio.create_subprocess_exec(
        *process_args,
        cwd=os.path.dirname(os.path.abspath(__file__)),
        env=env,
    )
    return proc



async def _fetch_agent_data(agent_id: Optional[str], agent_service: AgentService) -> Agent:
    """Helper to fetch agent data and handle errors."""
    if not agent_id:
        raise HTTPException(status_code=400, detail="Agent ID is required.")
    try:
        agent_data = await agent_service.get_agent(agent_id)
        return agent_data
    except HTTPException as e:
        logger.error(f"HTTP Exception while fetching agent {agent_id}: {e.detail}")
        raise
    except Exception as e:
        logger.error(f"Error fetching agent data for agent_id {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching agent data: {str(e)}"
        )


async def _create_room_and_token(daily_helpers):
    """Helper to create a Daily room and token, with error handling."""
    try:
        room = await daily_helpers["rest"].create_room(DailyRoomParams())
        if not room.url:
            raise HTTPException(status_code=500, detail="Failed to create Daily room")
        token = await daily_helpers["rest"].get_token(room.url)
        if not token:
            await delete_room(room.url) # Ensure room is deleted if token fails
            raise HTTPException(status_code=500, detail="Failed to generate Daily token")
        return room, token
    except Exception as e:
        logger.error(f"Error creating room and token: {str(e)}")
        raise


@app.get("/")
async def start_agent(
    request: Request,
    agent_id: Optional[str] = Query(None),
    daily_helpers = Depends(get_daily_helpers),
    agent_service: AgentService = Depends(get_agent_service),
    call_log_service: CallLogService = Depends(get_call_log_service) ,
    config: AppConfig = Depends(),
):
    """Browser access endpoint to start an agent in a Daily room."""
    agent_vad_config = None
    try:
        agent_data = await _fetch_agent_data(agent_id, agent_service)
        agent_config = agent_data.model_dump()

        room, token = await _create_room_and_token(daily_helpers)
        await asyncio.sleep(2)  # Short delay
        participants = await daily_helpers["rest"].get_participants(room.url)
        logger.debug(f"Initial participant check: {participants}")

        existing = sum(
            1
            for entry in active_rooms.values()
            if "proc" in entry
        )
        if existing >= MAX_BOTS_PER_ROOM:
            await delete_room(room.url)
            raise HTTPException(status_code=429, detail="Room capacity reached")

        bot_module = "bots.flow" if agent_data.type == "flow" else "bots.simple"

        agent_vad_config = agent_data.vad_config

        proc = await _start_bot_process(
            room.url,
            token,
            agent_config,
            bot_module,
            agent_data.stt_service,  # Correct: service name
            agent_data.stt_config,   # Correct: config
            agent_data.llm_service,
            agent_data.llm_config,
            agent_data.tts_service,
            agent_data.tts_config,
            agent_vad_config,
        )

         # Track resources
        bot_procs_global = request.app.state.bot_procs # Access bot_procs from app state
        bot_procs_global[proc.pid] = proc
        active_rooms[room.url] = {
            "pid": proc.pid,
            "created_at": time.time(),
            "last_checked": time.time(),
        }


        logger.info(f"Agent started in room {room.url}, Bot Instance: {proc}, Agent ID: {agent_id}")
        return RedirectResponse(room.url)

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Error starting agent: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start agent: {str(e)}")


@app.post("/connect")
async def rtvi_connect(
    request: Request,
    agent_id: Optional[str] = Query(None),
    daily_helpers = Depends(get_daily_helpers),
    agent_service: AgentService = Depends(get_agent_service),
    call_log_service: CallLogService = Depends(get_call_log_service),
    config: AppConfig = Depends()
):
    """RTVI client connection endpoint to start an agent and return room details."""
    agent_vad_config = None
    try:
        # Try to get agent_id from query parameter first, then from request body
        if not agent_id:
            try:
                body = await request.json()
                agent_id = body.get("agent_id")
            except:
                pass  # No JSON body or agent_id not in body

        agent_data = await _fetch_agent_data(agent_id, agent_service)
        agent_config = agent_data.model_dump()

        room, token = await _create_room_and_token(daily_helpers)

        bot_module = "bots.flow" if agent_data.type == "flow" else "bots.simple"

        agent_vad_config = agent_data.vad_config

        proc = await _start_bot_process(
                room.url,
                token,
                agent_config,
                bot_module,
                agent_data.stt_service,  # Correct: service name
                agent_data.stt_config,   # Correct: config
                agent_data.llm_service,
                agent_data.llm_config,
                agent_data.tts_service,
                agent_data.tts_config,
                agent_vad_config,
            )

        # active_rooms[room.url] = {
        #     "bot_instance": bot_instance,
        #     "created_at": time.time(),
        #     "last_checked": time.time(),
        # }
        # logger.info(f"Agent connected to room {room.url}, Bot Instance: {bot_instance}, Agent ID: {agent_id}")
        # return {"room_url": room.url, "token": token}

                # Track resources
        bot_procs_global = request.app.state.bot_procs # Access bot_procs from app state
        bot_procs_global[proc.pid] = proc
        active_rooms[room.url] = {
            "pid": proc.pid,
            "created_at": time.time(),
            "last_checked": time.time(),
        }
        logger.info(f"Agent connected to room {room.url}, PID: {proc.pid}, Agent ID: {agent_id}")
        return {"room_url": room.url, "token": token}

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Connection failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Connection failed: {str(e)}")



@app.get("/status/{pid}")
def get_status(pid: int, request: Request):
    """Check bot process status."""
    bot_procs_global = request.app.state.bot_procs # Access bot_procs from app state
    proc = bot_procs_global.get(pid)
    if not proc:
        raise HTTPException(status_code=404, detail="Process not found")

    return JSONResponse({
        "pid": pid,
        "status": "running" if proc.poll() is None else "exited",
        "room_url": next((url for url, info in active_rooms.items() if info["pid"] == pid), None)
    })

if __name__ == "__main__":
    import uvicorn

    logger.info(f"Starting server with {config.bot_type} bot configuration")
    uvicorn.run(
        "server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
    )