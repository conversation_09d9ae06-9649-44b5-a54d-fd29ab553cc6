#!/usr/bin/env python3
"""
Test to verify the PipecatWidget initialization fix.
"""

import requests
import sys

API_BASE_URL = "http://localhost:7860"

def main():
    print("🔧 PipecatWidget Initialization Fix")
    print("=" * 50)
    
    # Check server
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Server not running")
            return False
    except:
        print("❌ Server not accessible")
        return False
    
    print("✅ Server is running")
    
    # Get test agent
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            active_agents = [a for a in agents if a.get('status') == 'active']
            
            if active_agents:
                agent = active_agents[0]
                agent_id = agent['id']
                agent_name = agent['name']
                print(f"✅ Found test agent: {agent_name}")
                
                print(f"\n🔧 Initialization Fixes Applied:")
                print(f"   • Simplified initialization logic")
                print(f"   • Added timeout to prevent hanging on initDevices()")
                print(f"   • Improved error handling and fallbacks")
                print(f"   • Better UI state management")
                print(f"   • Enhanced debug logging")
                
                print(f"\n🧪 Testing Instructions:")
                print(f"1. Open http://localhost:3000/test")
                print(f"2. Select agent: {agent_name}")
                print(f"3. Check PipecatWidget (Original) section:")
                
                print(f"\n📊 Expected Behavior:")
                print(f"   ✅ Should show 'Loading...' briefly")
                print(f"   ✅ Should show 'Initializing audio devices...' briefly")
                print(f"   ✅ Should then show the microphone button")
                print(f"   ✅ Button should be enabled after initialization")
                print(f"   ✅ Should NOT get stuck on 'Initializing...'")
                
                print(f"\n🔍 Debug Console Should Show:")
                print(f"   ✅ 'PipecatWidget: Creating RTVI client for agent: {agent_id}'")
                print(f"   ✅ 'PipecatWidget: Starting initialization...'")
                print(f"   ✅ 'PipecatWidget: Initializing RTVI client...'")
                print(f"   ✅ 'PipecatWidget: Device initialization completed'")
                print(f"   ✅ 'PipecatWidget: RTVI client initialized successfully'")
                
                print(f"\n⚠️  If Still Stuck:")
                print(f"   • Check browser console for errors")
                print(f"   • Look for timeout or permission errors")
                print(f"   • Try refreshing the page")
                print(f"   • Ensure microphone permissions are granted")
                
                print(f"\n🎯 Key Changes Made:")
                print(f"   • Removed complex initialization guards")
                print(f"   • Added 10-second timeout for initDevices()")
                print(f"   • Set isInitialized=true even on errors (with error message)")
                print(f"   • Improved UI state transitions")
                print(f"   • Better error messages for debugging")
                
                return True
            else:
                print("❌ No active agents found")
                print("   Run: python create_test_agent.py")
                return False
        else:
            print(f"❌ Failed to get agents: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
