#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a test agent for development and testing.
"""

import requests
import json
import sys

API_BASE_URL = "http://localhost:7860"

def create_test_agent():
    """Create a simple test agent."""
    
    test_agent = {
        "name": "Test Assistant",
        "description": "A simple test agent for development and testing",
        "type": "simple",
        "status": "active",
        "category": "test",
        "stt_service": "deepgram",
        "llm_service": "openai",
        "tts_service": "elevenlabs",
        "stt_config": {
            "model": "nova-2",
            "language": "en-US"
        },
        "llm_config": {
            "model": "gpt-4o",
            "temperature": 0.7,
            "max_tokens": 150
        },
        "tts_config": {
            "voice_id": "21m00Tcm4TlvDq8ikWAM",
            "model_id": "eleven_monolingual_v1"
        },
        "configuration": {
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a helpful test assistant. Keep your responses short and friendly. Always confirm that you can hear the user clearly."
                }
            ]
        }
    }
    
    try:
        print("Creating test agent...")
        response = requests.post(
            f"{API_BASE_URL}/agents/",
            json=test_agent,
            timeout=10
        )
        
        if response.status_code == 201:
            agent_data = response.json()
            agent_id = agent_data.get("id")
            print(f"✅ Successfully created test agent!")
            print(f"   Agent ID: {agent_id}")
            print(f"   Name: {agent_data.get('name')}")
            print(f"   Status: {agent_data.get('status')}")
            print(f"\n🔗 Test URLs:")
            print(f"   Dashboard: http://localhost:3000")
            print(f"   Test Page: http://localhost:3000/test")
            print(f"   Direct: http://localhost:7860/?agent_id={agent_id}")
            return True
        else:
            print(f"❌ Failed to create test agent: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   Error: {error_detail}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to create test agent: {e}")
        print("   Make sure the server is running with: python server.py")
        return False

def check_existing_agents():
    """Check if there are existing agents."""
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=5)
        if response.status_code == 200:
            agents = response.json()
            print(f"Found {len(agents)} existing agents:")
            for agent in agents:
                print(f"  - {agent.get('name')} (ID: {agent.get('id')}, Status: {agent.get('status')})")
            return agents
        else:
            print(f"Failed to fetch agents: {response.status_code}")
            return []
    except requests.exceptions.RequestException as e:
        print(f"Failed to check existing agents: {e}")
        return []

def main():
    print("🤖 VoxDiscover Test Agent Creator")
    print("=" * 40)
    
    # Check server connection
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding correctly")
            print("   Make sure the server is running with: python server.py")
            return False
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server")
        print("   Make sure the server is running with: python server.py")
        return False
    
    print("✅ Server is running")
    
    # Check existing agents
    existing_agents = check_existing_agents()
    
    if existing_agents:
        active_agents = [a for a in existing_agents if a.get('status') == 'active']
        if active_agents:
            print(f"\n✅ Found {len(active_agents)} active agents - you can start testing!")
            print("\n🔗 Test URLs:")
            print("   Dashboard: http://localhost:3000")
            print("   Test Page: http://localhost:3000/test")
            return True
    
    # Create test agent
    print("\nNo active agents found. Creating a test agent...")
    success = create_test_agent()
    
    if success:
        print("\n🎉 Test agent created successfully!")
        print("\n📝 Next steps:")
        print("1. Start the frontend: cd client && npm run dev")
        print("2. Open http://localhost:3000/test in your browser")
        print("3. Select the test agent and click the microphone to start talking")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
