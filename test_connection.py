#!/usr/bin/env python3
"""
Test script to verify the connection endpoint works correctly.
"""

import requests
import json
import sys

API_BASE_URL = "http://localhost:7860"

def test_connect_endpoint():
    """Test the /connect endpoint with different agent_id scenarios."""
    
    print("🔍 Testing /connect endpoint...")
    
    # Test 1: Connect without agent_id (should fail gracefully)
    print("\n1. Testing connection without agent_id:")
    try:
        response = requests.post(f"{API_BASE_URL}/connect", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 400:
            print("   ✅ Correctly rejected connection without agent_id")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test 2: Connect with invalid agent_id
    print("\n2. Testing connection with invalid agent_id:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/connect?agent_id=invalid-id", 
            timeout=10
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 404:
            print("   ✅ Correctly rejected invalid agent_id")
        else:
            print(f"   ⚠️  Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test 3: Get available agents
    print("\n3. Getting available agents:")
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            print(f"   ✅ Found {len(agents)} agents")
            
            active_agents = [a for a in agents if a.get('status') == 'active']
            if active_agents:
                print(f"   ✅ Found {len(active_agents)} active agents")
                
                # Test 4: Connect with valid agent_id
                test_agent = active_agents[0]
                agent_id = test_agent.get('id')
                agent_name = test_agent.get('name')
                
                print(f"\n4. Testing connection with valid agent_id ({agent_name}):")
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/connect?agent_id={agent_id}",
                        timeout=15
                    )
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        if 'room_url' in data and 'token' in data:
                            print("   ✅ Successfully created room and token")
                            print(f"   Room URL: {data['room_url'][:50]}...")
                            print(f"   Token: {data['token'][:20]}...")
                        else:
                            print(f"   ⚠️  Missing room_url or token in response")
                            print(f"   Response: {data}")
                    else:
                        print(f"   ❌ Connection failed with status: {response.status_code}")
                        try:
                            error_data = response.json()
                            print(f"   Error: {error_data}")
                        except:
                            print(f"   Response: {response.text}")
                            
                except Exception as e:
                    print(f"   ❌ Request failed: {e}")
            else:
                print("   ⚠️  No active agents found - create an active agent first")
        else:
            print(f"   ❌ Failed to get agents: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Failed to get agents: {e}")

def test_request_body_format():
    """Test the /connect endpoint with request body format (RTVI client format)."""
    
    print("\n🔍 Testing /connect endpoint with request body format...")
    
    # Get an active agent first
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            active_agents = [a for a in agents if a.get('status') == 'active']
            
            if active_agents:
                test_agent = active_agents[0]
                agent_id = test_agent.get('id')
                agent_name = test_agent.get('name')
                
                print(f"Testing with agent: {agent_name} (ID: {agent_id})")
                
                # Test with request body (RTVI client format)
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/connect",
                        json={"agent_id": agent_id},
                        headers={"Content-Type": "application/json"},
                        timeout=15
                    )
                    print(f"Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        if 'room_url' in data and 'token' in data:
                            print("✅ Successfully created room and token via request body")
                            print(f"Room URL: {data['room_url'][:50]}...")
                            print(f"Token: {data['token'][:20]}...")
                        else:
                            print(f"⚠️  Missing room_url or token in response")
                            print(f"Response: {data}")
                    else:
                        print(f"❌ Connection failed with status: {response.status_code}")
                        try:
                            error_data = response.json()
                            print(f"Error: {error_data}")
                        except:
                            print(f"Response: {response.text}")
                            
                except Exception as e:
                    print(f"❌ Request failed: {e}")
            else:
                print("⚠️  No active agents found - create an active agent first")
        else:
            print(f"❌ Failed to get agents: {response.status_code}")
    except Exception as e:
        print(f"❌ Failed to get agents: {e}")

def main():
    """Run connection tests."""
    print("🚀 VoxDiscover Connection Test")
    print("=" * 50)
    
    # Check server connection
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding correctly")
            print("   Make sure the server is running with: python server.py")
            return False
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server")
        print("   Make sure the server is running with: python server.py")
        return False
    
    print("✅ Server is running")
    
    # Run tests
    test_connect_endpoint()
    test_request_body_format()
    
    print("\n" + "=" * 50)
    print("🎉 Connection tests completed!")
    print("\n📝 Next steps:")
    print("1. If tests passed, start the frontend: cd client && npm run dev")
    print("2. Open http://localhost:3000/test in your browser")
    print("3. Select an agent and test the voice connection")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
