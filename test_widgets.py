#!/usr/bin/env python3
"""
Quick test to verify both widgets are working correctly.
"""

import requests
import sys

API_BASE_URL = "http://localhost:7860"

def main():
    print("🧪 Widget Functionality Test")
    print("=" * 40)
    
    # Check server
    try:
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ Server not running")
            return False
    except:
        print("❌ Server not accessible")
        return False
    
    print("✅ Server is running")
    
    # Get test agent
    try:
        response = requests.get(f"{API_BASE_URL}/agents/", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            active_agents = [a for a in agents if a.get('status') == 'active']
            
            if active_agents:
                agent = active_agents[0]
                agent_id = agent['id']
                agent_name = agent['name']
                print(f"✅ Found test agent: {agent_name}")
                
                # Test connection endpoint
                print("\n🔍 Testing connection endpoint...")
                response = requests.post(
                    f"{API_BASE_URL}/connect?agent_id={agent_id}",
                    timeout=15
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'room_url' in data and 'token' in data:
                        print("✅ Connection endpoint working")
                        
                        print(f"\n🎉 Both widgets should now work!")
                        print(f"📱 Test URLs:")
                        print(f"   • Test Page: http://localhost:3000/test")
                        print(f"   • Dashboard: http://localhost:3000")
                        print(f"   • Direct: {API_BASE_URL}/?agent_id={agent_id}")
                        
                        print(f"\n📝 Testing Instructions:")
                        print(f"1. Open http://localhost:3000/test")
                        print(f"2. Select agent: {agent_name}")
                        print(f"3. Test both widgets:")
                        print(f"   • Simple Voice Widget (should work)")
                        print(f"   • Pipecat Widget (should now work too)")
                        print(f"4. Both should connect and maintain stable connections")
                        
                        return True
                    else:
                        print("❌ Invalid response from connection endpoint")
                        return False
                else:
                    print(f"❌ Connection endpoint failed: {response.status_code}")
                    return False
            else:
                print("❌ No active agents found")
                print("   Run: python create_test_agent.py")
                return False
        else:
            print(f"❌ Failed to get agents: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
