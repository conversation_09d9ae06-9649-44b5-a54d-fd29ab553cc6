# utils/daily_transport_wrapper.py
from pipecat.transports.services.daily import DailyTransport
from loguru import logger

class DailyTransportWrapper:
    def __init__(self, daily_transport: DailyTransport):
        self.daily_transport = daily_transport
        self._client = daily_transport._client  # Keep the _client reference
        logger.debug(f"DailyTransportWrapper.__init__: Wrapped transport: {self.daily_transport}, type: {type(self.daily_transport)}")

    def event_handler(self, event_name: str):
        """Delegate event_handler calls to the underlying DailyTransport."""
        logger.debug(f"DailyTransportWrapper.event_handler called for event: {event_name}")
        return self.daily_transport.event_handler(event_name)

    @property
    def room_url(self):
        """Expose room_url from the underlying DailyTransport."""
        # Access the _room_url from the underlying DailyTransport
        return self.daily_transport._room_url


    async def leave(self):
        """Leaves the Daily room."""
        if self._client:  # Check if _client is still valid
            await self._client.leave()

    async def cleanup(self):
        """Cleans up resources, including leaving the Daily room."""
        await self.daily_transport.cleanup()  # Cleanup the DailyTransport
        if self._client:
                    await self._client.leave()

    def __getattr__(self, name):
        """
        Delegate attribute access to the underlying DailyTransport object.
        """
        logger.debug(f"DailyTransportWrapper.__getattr__ called for: {name}")
        return getattr(self.daily_transport, name)