# utils/agent_templates.py
from typing import List, Dict, Any
from utils.agent_model import AgentTemplate, AgentType, STTServiceType, LLMServiceType, TTSServiceType

# Predefined agent templates
AGENT_TEMPLATES: List[AgentTemplate] = [
    AgentTemplate(
        id="customer_support",
        name="Customer Support Assistant",
        description="A helpful customer support agent that can answer questions and resolve issues",
        category="customer_service",
        agent_type=AgentType.SIMPLE,
        stt_service=STTServiceType.DEEPGRAM,
        llm_service=LLMServiceType.OPENAI,
        tts_service=TTSServiceType.ELEVENLABS,
        default_config={
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a helpful customer support assistant. Be polite, professional, and try to resolve customer issues efficiently. Keep responses concise and ask clarifying questions when needed."
                }
            ]
        },
        stt_config={
            "model": "nova-2",
            "language": "en-US"
        },
        llm_config={
            "model": "gpt-4o",
            "temperature": 0.7,
            "max_tokens": 150
        },
        tts_config={
            "voice_id": "21m00Tcm4TlvDq8ikWAM",
            "model_id": "eleven_monolingual_v1"
        },
        conversation_config={
            "starting_phrase": "Hello! I'm your customer support assistant. How can I help you today?"
        }
    ),

    AgentTemplate(
        id="sales_assistant",
        name="Sales Assistant",
        description="An engaging sales assistant that can qualify leads and schedule demos",
        category="sales",
        agent_type=AgentType.FLOW,
        stt_service=STTServiceType.DEEPGRAM,
        llm_service=LLMServiceType.OPENAI,
        tts_service=TTSServiceType.ELEVENLABS,
        default_config={
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a friendly sales assistant. Your goal is to understand the prospect's needs, qualify them, and schedule a demo if appropriate. Be conversational and helpful."
                }
            ]
        },
        stt_config={
            "model": "nova-2",
            "language": "en-US"
        },
        llm_config={
            "model": "gpt-4o",
            "temperature": 0.8,
            "max_tokens": 200
        },
        tts_config={
            "voice_id": "EXAVITQu4vr4xnSDxMaL",
            "model_id": "eleven_monolingual_v1"
        },
        conversation_config={
            "starting_phrase": "Hi there! I'm a sales assistant. I'd love to learn about your needs and see how we can help you."
        }
    ),

    AgentTemplate(
        id="appointment_scheduler",
        name="Appointment Scheduler",
        description="An efficient appointment scheduling assistant with calendar integration",
        category="scheduling",
        agent_type=AgentType.FLOW,
        stt_service=STTServiceType.DEEPGRAM,
        llm_service=LLMServiceType.OPENAI,
        tts_service=TTSServiceType.DEEPGRAM,
        default_config={
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are an appointment scheduling assistant. Help users find available time slots and book appointments efficiently. Be clear about availability and confirm all details."
                }
            ]
        },
        stt_config={
            "model": "nova-2",
            "language": "en-US"
        },
        llm_config={
            "model": "gpt-4o",
            "temperature": 0.5,
            "max_tokens": 150
        },
        tts_config={
            "voice": "aura-helios-en"
        }
    ),

    AgentTemplate(
        id="healthcare_assistant",
        name="Healthcare Assistant",
        description="A compassionate healthcare assistant for patient inquiries and appointment scheduling",
        category="healthcare",
        agent_type=AgentType.SIMPLE,
        stt_service=STTServiceType.DEEPGRAM,
        llm_service=LLMServiceType.ANTHROPIC,
        tts_service=TTSServiceType.ELEVENLABS,
        default_config={
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a compassionate healthcare assistant. Help patients with general inquiries, appointment scheduling, and provide basic health information. Always remind patients to consult with healthcare professionals for medical advice."
                }
            ]
        },
        stt_config={
            "model": "nova-2",
            "language": "en-US"
        },
        llm_config={
            "model": "claude-3-sonnet-20240229",
            "temperature": 0.6,
            "max_tokens": 200
        },
        tts_config={
            "voice_id": "ThT5KcBeYPX3keUQqHPh",
            "model_id": "eleven_monolingual_v1"
        }
    ),

    AgentTemplate(
        id="restaurant_host",
        name="Restaurant Host",
        description="A friendly restaurant host for taking reservations and answering menu questions",
        category="hospitality",
        agent_type=AgentType.SIMPLE,
        stt_service=STTServiceType.DEEPGRAM,
        llm_service=LLMServiceType.OPENAI,
        tts_service=TTSServiceType.CARTESIA,
        default_config={
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a friendly restaurant host. Help customers make reservations, answer questions about the menu, hours, and location. Be warm and welcoming in your responses."
                }
            ]
        },
        stt_config={
            "model": "nova-2",
            "language": "en-US"
        },
        llm_config={
            "model": "gpt-4o",
            "temperature": 0.8,
            "max_tokens": 150
        },
        tts_config={
            "voice_id": "a0e99841-438c-4a64-b679-ae501e7d6091",
            "model_id": "sonic-english"
        },
        conversation_config={
            "starting_phrase": "Welcome! Thank you for calling. I'm here to help with reservations and answer any questions about our restaurant."
        }
    ),

    AgentTemplate(
        id="tech_support",
        name="Technical Support",
        description="A knowledgeable technical support agent for troubleshooting and guidance",
        category="technical",
        agent_type=AgentType.SIMPLE,
        stt_service=STTServiceType.DEEPGRAM,
        llm_service=LLMServiceType.OPENAI,
        tts_service=TTSServiceType.OPENAI,
        default_config={
            "initial_messages": [
                {
                    "role": "system",
                    "content": "You are a knowledgeable technical support agent. Help users troubleshoot technical issues, provide step-by-step guidance, and escalate complex issues when necessary. Be patient and clear in your explanations."
                }
            ]
        },
        stt_config={
            "model": "nova-2",
            "language": "en-US"
        },
        llm_config={
            "model": "gpt-4o",
            "temperature": 0.3,
            "max_tokens": 250
        },
        tts_config={
            "voice": "alloy",
            "model": "tts-1"
        }
    )
]

class AgentTemplateService:
    """Service for managing agent templates"""

    @staticmethod
    def get_all_templates() -> List[AgentTemplate]:
        """Get all available agent templates"""
        return AGENT_TEMPLATES

    @staticmethod
    def get_template_by_id(template_id: str) -> AgentTemplate:
        """Get a specific template by ID"""
        for template in AGENT_TEMPLATES:
            if template.id == template_id:
                return template
        raise ValueError(f"Template with ID '{template_id}' not found")

    @staticmethod
    def get_templates_by_category(category: str) -> List[AgentTemplate]:
        """Get templates by category"""
        return [template for template in AGENT_TEMPLATES if template.category == category]

    @staticmethod
    def get_categories() -> List[str]:
        """Get all available categories"""
        return list(set(template.category for template in AGENT_TEMPLATES))

    @staticmethod
    def create_agent_from_template(template_id: str, name: str, description: str = None) -> Dict[str, Any]:
        """Create an agent configuration from a template"""
        template = AgentTemplateService.get_template_by_id(template_id)

        return {
            "name": name,
            "description": description or template.description,
            "type": template.agent_type.value,
            "category": template.category,
            "stt_service": template.stt_service.value,
            "llm_service": template.llm_service.value,
            "tts_service": template.tts_service.value,
            "stt_config": template.stt_config,
            "llm_config": template.llm_config,
            "tts_config": template.tts_config,
            "vad_config": template.vad_config,
            "conversation_config": template.conversation_config,
            "configuration": template.default_config
        }
