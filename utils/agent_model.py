# utils/agent_model.py
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum

# --- Enums for Service Types ---
class STTServiceType(str, Enum):
    DEEPGRAM = "deepgram"
    OPENAI_WHISPER = "openai_whisper"
    AZURE = "azure"
    GOOGLE = "google"
    ASSEMBLYAI = "assemblyai"
    GROQ = "groq"
    GLADIA = "gladia"
    WHISPER = "whisper"

class LLMServiceType(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    GROQ = "groq"
    DEEPSEEK = "deepseek"
    TOGETHER = "together"
    FIREWORKS = "fireworks"
    OLLAMA = "ollama"
    PERPLEXITY = "perplexity"
    CEREBRAS = "cerebras"

class TTSServiceType(str, Enum):
    DEEPGRAM = "deepgram"
    ELEVENLABS = "elevenlabs"
    OPENAI = "openai"
    AZURE = "azure"
    CARTESIA = "cartesia"
    PLAYHT = "playht"
    LMNT = "lmnt"
    FISH = "fish"
    NEUPHONIC = "neuphonic"

class S2SServiceType(str, Enum):
    OPENAI_REALTIME = "openai_realtime"
    GEMINI_MULTIMODAL = "gemini_multimodal"

class VADServiceType(str, Enum):
    SILERO = "silero"
    WEBRTC = "webrtc"

class AgentType(str, Enum):
    SIMPLE = "simple"
    FLOW = "flow"

class AgentStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"

# --- Service Configuration Models ---

class STTConfig(BaseModel):
    """Speech-to-Text service configuration"""
    # Common parameters
    language: Optional[str] = Field(default="en-US", description="Language code")
    model: Optional[str] = Field(default=None, description="Model identifier")

    # Deepgram specific
    smart_format: Optional[bool] = Field(default=True, description="Enable smart formatting")
    punctuate: Optional[bool] = Field(default=True, description="Enable punctuation")
    profanity_filter: Optional[bool] = Field(default=True, description="Filter profanity")
    vad_events: Optional[bool] = Field(default=False, description="Voice activity detection events")
    interim_results: Optional[bool] = Field(default=True, description="Return interim results")

    # OpenAI Whisper specific
    temperature: Optional[float] = Field(default=0.0, ge=0.0, le=1.0, description="Sampling temperature")
    response_format: Optional[str] = Field(default="json", description="Response format")

    # AssemblyAI specific
    speaker_labels: Optional[bool] = Field(default=False, description="Enable speaker diarization")
    sentiment_analysis: Optional[bool] = Field(default=False, description="Enable sentiment analysis")
    language_detection: Optional[bool] = Field(default=False, description="Auto-detect language")

    # Azure specific
    word_level_timestamps: Optional[bool] = Field(default=False, description="Word-level timestamps")

    # Google specific
    enable_automatic_punctuation: Optional[bool] = Field(default=True, description="Auto punctuation")
    enable_word_time_offsets: Optional[bool] = Field(default=False, description="Word time offsets")

class LLMConfig(BaseModel):
    """Large Language Model service configuration"""
    # Common parameters
    model: str = Field(..., description="Model identifier")
    temperature: Optional[float] = Field(default=0.7, ge=0.0, le=2.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(default=1000, ge=1, le=8192, description="Maximum tokens to generate")

    # OpenAI/Anthropic/Most providers
    top_p: Optional[float] = Field(default=1.0, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    frequency_penalty: Optional[float] = Field(default=0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(default=0.0, ge=-2.0, le=2.0, description="Presence penalty")

    # Anthropic specific
    top_k: Optional[int] = Field(default=None, ge=1, description="Top-k sampling")

    # Google Gemini specific
    safety_settings: Optional[Dict[str, Any]] = Field(default=None, description="Safety filter settings")

    # System prompt
    system_prompt: Optional[str] = Field(default=None, description="System message/prompt")

    # Advanced parameters
    seed: Optional[int] = Field(default=None, description="Random seed for reproducibility")
    stream: Optional[bool] = Field(default=True, description="Enable streaming responses")

class TTSConfig(BaseModel):
    """Text-to-Speech service configuration"""
    # Common parameters
    voice_id: Optional[str] = Field(default=None, description="Voice identifier")
    model: Optional[str] = Field(default=None, description="Model identifier")
    speed: Optional[float] = Field(default=1.0, ge=0.25, le=4.0, description="Speech speed multiplier")

    # ElevenLabs specific
    stability: Optional[float] = Field(default=0.5, ge=0.0, le=1.0, description="Voice stability")
    similarity_boost: Optional[float] = Field(default=0.8, ge=0.0, le=1.0, description="Similarity boost")
    style: Optional[float] = Field(default=0.0, ge=0.0, le=1.0, description="Style exaggeration")
    use_speaker_boost: Optional[bool] = Field(default=True, description="Use speaker boost")

    # OpenAI specific
    voice: Optional[str] = Field(default="alloy", description="Voice name")
    response_format: Optional[str] = Field(default="mp3", description="Audio format")

    # Azure specific
    voice_name: Optional[str] = Field(default="en-US-JennyNeural", description="Azure voice name")
    speaking_rate: Optional[float] = Field(default=1.0, ge=0.5, le=2.0, description="Speaking rate")
    speaking_volume: Optional[float] = Field(default=1.0, ge=0.0, le=1.0, description="Speaking volume")
    pitch: Optional[str] = Field(default="medium", description="Voice pitch")

    # Cartesia specific
    emotion: Optional[str] = Field(default="neutral", description="Emotional tone")

    # Audio format settings
    sample_rate: Optional[int] = Field(default=24000, description="Audio sample rate")
    encoding: Optional[str] = Field(default="linear16", description="Audio encoding")

class VADConfig(BaseModel):
    """Voice Activity Detection configuration"""
    provider: Optional[VADServiceType] = Field(default=VADServiceType.SILERO, description="VAD provider")
    threshold: Optional[float] = Field(default=0.5, ge=0.0, le=1.0, description="Detection threshold")
    min_silence_duration: Optional[int] = Field(default=500, ge=100, description="Min silence duration (ms)")
    min_speech_duration: Optional[int] = Field(default=250, ge=100, description="Min speech duration (ms)")
    window_size: Optional[int] = Field(default=512, description="Analysis window size")

class ConversationConfig(BaseModel):
    """Conversation flow configuration"""
    interruption_enabled: Optional[bool] = Field(default=True, description="Allow interruptions")
    response_timeout: Optional[int] = Field(default=5000, ge=1000, description="Response timeout (ms)")
    turn_detection_sensitivity: Optional[float] = Field(default=0.7, ge=0.0, le=1.0, description="Turn detection sensitivity")
    max_conversation_length: Optional[int] = Field(default=50, ge=1, description="Max conversation turns")
    context_window: Optional[int] = Field(default=4000, ge=100, description="Context window size")
    starting_phrase: Optional[str] = Field(default=None, description="Optional greeting message said at conversation start")

# --- Agent Template Model ---
class AgentTemplate(BaseModel):
    id: str
    name: str
    description: str
    category: str
    agent_type: AgentType
    default_config: Dict[str, Any]
    stt_service: STTServiceType
    llm_service: LLMServiceType
    tts_service: TTSServiceType
    stt_config: Dict[str, Any]
    llm_config: Dict[str, Any]
    tts_config: Dict[str, Any]
    vad_config: Optional[Dict[str, Any]] = None
    conversation_config: Optional[Dict[str, Any]] = None

# --- Enhanced Agent Data Model ---
class Agent(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    type: AgentType = Field(..., description="Type of agent (simple or flow)")
    status: AgentStatus = Field(default=AgentStatus.ACTIVE, description="Agent status")
    category: str = Field(default="general", description="Agent category")

    # Service Configuration
    stt_service: Optional[STTServiceType] = None
    llm_service: Optional[LLMServiceType] = None
    tts_service: Optional[TTSServiceType] = None
    s2s_service: Optional[S2SServiceType] = None

    # Enhanced Service-specific configurations using typed models
    stt_config: Optional[STTConfig] = Field(default_factory=STTConfig, description="STT service configuration")
    llm_config: Optional[LLMConfig] = Field(default=None, description="LLM service configuration")
    tts_config: Optional[TTSConfig] = Field(default_factory=TTSConfig, description="TTS service configuration")
    vad_config: Optional[VADConfig] = Field(default_factory=VADConfig, description="VAD configuration")
    conversation_config: Optional[ConversationConfig] = Field(default_factory=ConversationConfig, description="Conversation flow configuration")

    # Agent behavior configuration (legacy support)
    configuration: Optional[Dict[str, Any]] = None

    # Metadata
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    created_by: Optional[str] = None
    tags: Optional[List[str]] = None

    # Usage statistics
    total_calls: Optional[int] = Field(default=0, description="Total number of calls")
    total_duration: Optional[int] = Field(default=0, description="Total call duration in seconds")
    last_used: Optional[str] = None

    # Integration settings
    webhook_url: Optional[str] = None
    custom_headers: Optional[Dict[str, str]] = None

    class Config:
        from_attributes = True
        use_enum_values = True

    @classmethod
    def model_validate(cls, obj):
        """Override model_validate to handle database objects with nested configs"""
        if isinstance(obj, dict):
            # Handle None status by setting default
            if obj.get('status') is None:
                obj['status'] = AgentStatus.ACTIVE.value

            # Handle None category by setting default
            if obj.get('category') is None:
                obj['category'] = 'general'

            # Convert nested config dictionaries to proper models
            if 'stt_config' in obj and isinstance(obj['stt_config'], dict):
                obj['stt_config'] = STTConfig(**obj['stt_config']) if obj['stt_config'] else None
            if 'llm_config' in obj and isinstance(obj['llm_config'], dict):
                obj['llm_config'] = LLMConfig(**obj['llm_config']) if obj['llm_config'] else None
            if 'tts_config' in obj and isinstance(obj['tts_config'], dict):
                obj['tts_config'] = TTSConfig(**obj['tts_config']) if obj['tts_config'] else None
            if 'vad_config' in obj and isinstance(obj['vad_config'], dict):
                obj['vad_config'] = VADConfig(**obj['vad_config']) if obj['vad_config'] else None
            if 'conversation_config' in obj and isinstance(obj['conversation_config'], dict):
                obj['conversation_config'] = ConversationConfig(**obj['conversation_config']) if obj['conversation_config'] else None

        return super().model_validate(obj)

    def model_dump(self, **kwargs):
        """Override model_dump method to handle nested models properly"""
        data = super().model_dump(**kwargs)

        # Convert nested config models to dicts
        if self.stt_config and hasattr(self.stt_config, 'model_dump'):
            data['stt_config'] = self.stt_config.model_dump()
        if self.llm_config and hasattr(self.llm_config, 'model_dump'):
            data['llm_config'] = self.llm_config.model_dump()
        if self.tts_config and hasattr(self.tts_config, 'model_dump'):
            data['tts_config'] = self.tts_config.model_dump()
        if self.vad_config and hasattr(self.vad_config, 'model_dump'):
            data['vad_config'] = self.vad_config.model_dump()
        if self.conversation_config and hasattr(self.conversation_config, 'model_dump'):
            data['conversation_config'] = self.conversation_config.model_dump()

        return data

    # Backward compatibility
    def dict(self, **kwargs):
        """Backward compatibility method"""
        return self.model_dump(**kwargs)

    def json(self, **kwargs):
        """Backward compatibility method"""
        return self.model_dump_json(**kwargs)

# --- Agent Test Session Model ---
class AgentTestSession(BaseModel):
    id: Optional[str] = None
    agent_id: str
    room_url: str
    token: str
    status: str = "active"
    created_at: Optional[str] = None
    expires_at: Optional[str] = None

# --- Agent Analytics Model ---
class AgentAnalytics(BaseModel):
    agent_id: str
    date: str
    total_calls: int = 0
    total_duration: int = 0
    average_duration: float = 0.0
    success_rate: float = 0.0
    error_count: int = 0

# --- Service Capability Models ---

class ServiceCapability(BaseModel):
    """Service capability and metadata"""
    service_type: str = Field(..., description="Service type (stt, llm, tts, etc.)")
    service_name: str = Field(..., description="Service provider name")
    display_name: str = Field(..., description="Human-readable service name")
    description: str = Field(..., description="Service description")

    # Capabilities
    supported_languages: List[str] = Field(default_factory=list, description="Supported language codes")
    supported_models: List[str] = Field(default_factory=list, description="Available models")
    features: List[str] = Field(default_factory=list, description="Supported features")

    # Configuration schema
    config_schema: Dict[str, Any] = Field(default_factory=dict, description="JSON schema for configuration")

    # Metadata
    pricing_tier: Optional[str] = Field(default=None, description="Pricing tier (free, paid, enterprise)")
    latency_rating: Optional[str] = Field(default=None, description="Latency rating (low, medium, high)")
    quality_rating: Optional[str] = Field(default=None, description="Quality rating (basic, good, excellent)")
    requires_api_key: bool = Field(default=True, description="Requires API key")

class ServiceConfigurationTemplate(BaseModel):
    """Pre-configured service templates for common use cases"""
    id: str = Field(..., description="Template identifier")
    name: str = Field(..., description="Template name")
    description: str = Field(..., description="Template description")
    service_type: str = Field(..., description="Service type")
    service_name: str = Field(..., description="Service provider")
    use_case: str = Field(..., description="Intended use case")
    configuration: Dict[str, Any] = Field(..., description="Template configuration")
    tags: List[str] = Field(default_factory=list, description="Template tags")

class AgentConfigurationPreset(BaseModel):
    """Complete agent configuration presets"""
    id: str = Field(..., description="Preset identifier")
    name: str = Field(..., description="Preset name")
    description: str = Field(..., description="Preset description")
    category: str = Field(..., description="Preset category")
    use_case: str = Field(..., description="Intended use case")

    # Service selections
    stt_service: STTServiceType = Field(..., description="STT service")
    llm_service: LLMServiceType = Field(..., description="LLM service")
    tts_service: TTSServiceType = Field(..., description="TTS service")

    # Configurations
    stt_config: STTConfig = Field(..., description="STT configuration")
    llm_config: LLMConfig = Field(..., description="LLM configuration")
    tts_config: TTSConfig = Field(..., description="TTS configuration")
    vad_config: VADConfig = Field(default_factory=VADConfig, description="VAD configuration")
    conversation_config: ConversationConfig = Field(default_factory=ConversationConfig, description="Conversation configuration")

    # Metadata
    performance_profile: Optional[str] = Field(default=None, description="Performance profile (speed, balanced, quality)")
    cost_profile: Optional[str] = Field(default=None, description="Cost profile (budget, standard, premium)")
    complexity_level: Optional[str] = Field(default=None, description="Complexity level (basic, intermediate, advanced)")
    tags: List[str] = Field(default_factory=list, description="Preset tags")