# utils/agent_model.py
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum

# --- Enums for Service Types ---
class STTServiceType(str, Enum):
    DEEPGRAM = "deepgram"
    OPENAI_WHISPER = "openai_whisper"
    AZURE = "azure"
    GOOGLE = "google"

class LLMServiceType(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    GROQ = "groq"
    DEEPSEEK = "deepseek"

class TTSServiceType(str, Enum):
    DEEPGRAM = "deepgram"
    ELEVENLABS = "elevenlabs"
    OPENAI = "openai"
    AZURE = "azure"
    CARTESIA = "cartesia"

class AgentType(str, Enum):
    SIMPLE = "simple"
    FLOW = "flow"

class AgentStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"

# --- Agent Template Model ---
class AgentTemplate(BaseModel):
    id: str
    name: str
    description: str
    category: str
    agent_type: AgentType
    default_config: Dict[str, Any]
    stt_service: STTServiceType
    llm_service: LLMServiceType
    tts_service: TTSServiceType
    stt_config: Dict[str, Any]
    llm_config: Dict[str, Any]
    tts_config: Dict[str, Any]
    vad_config: Optional[Dict[str, Any]] = None

# --- Enhanced Agent Data Model ---
class Agent(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    type: AgentType = Field(..., description="Type of agent (simple or flow)")
    status: AgentStatus = Field(default=AgentStatus.ACTIVE, description="Agent status")
    category: Optional[str] = Field(default="general", description="Agent category")

    # Service Configuration
    stt_service: Optional[STTServiceType] = None
    llm_service: Optional[LLMServiceType] = None
    tts_service: Optional[TTSServiceType] = None

    # Service-specific configurations
    stt_config: Optional[Dict[str, Any]] = None
    llm_config: Optional[Dict[str, Any]] = None
    tts_config: Optional[Dict[str, Any]] = None
    vad_config: Optional[Dict[str, Any]] = None

    # Agent behavior configuration
    configuration: Optional[Dict[str, Any]] = None

    # Metadata
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    created_by: Optional[str] = None
    tags: Optional[List[str]] = None

    # Usage statistics
    total_calls: Optional[int] = Field(default=0, description="Total number of calls")
    total_duration: Optional[int] = Field(default=0, description="Total call duration in seconds")
    last_used: Optional[str] = None

    # Integration settings
    webhook_url: Optional[str] = None
    custom_headers: Optional[Dict[str, str]] = None

    class Config:
        orm_mode = True
        use_enum_values = True

# --- Agent Test Session Model ---
class AgentTestSession(BaseModel):
    id: Optional[str] = None
    agent_id: str
    room_url: str
    token: str
    status: str = "active"
    created_at: Optional[str] = None
    expires_at: Optional[str] = None

# --- Agent Analytics Model ---
class AgentAnalytics(BaseModel):
    agent_id: str
    date: str
    total_calls: int = 0
    total_duration: int = 0
    average_duration: float = 0.0
    success_rate: float = 0.0
    error_count: int = 0