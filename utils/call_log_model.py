from typing import Optional
from pydantic import BaseModel

class CallLogEntry(BaseModel):
    id: Optional[str] = None  # UUID, auto-generated by Supabase
    created_at: Optional[str] = None # Timestamp, auto-generated by Supabase
    agent_id: Optional[str] = None  # UUID of the Agent
    room_url: str
    start_time: Optional[str] = None # Timestamp
    end_time: Optional[str] = None   # Timestamp
    call_duration_seconds: Optional[int] = None
    full_transcript: Optional[str] = None
    status: Optional[str] = "running" # Default status
    error_message: Optional[str] = None

    class Config:
        orm_mode = True