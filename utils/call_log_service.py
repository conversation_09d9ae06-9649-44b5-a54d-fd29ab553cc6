from typing import List, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from supabase import Client
from loguru import logger

from .call_log_model import CallLogEntry

class CallLogService:
    def __init__(self, supabase_client: Client):
        self.db = supabase_client

    async def create_call_log_entry(self, call_log_entry: CallLogEntry) -> CallLogEntry:
        logger.debug(f"Creating call log entry: {call_log_entry}") # Add this
        data = (
            self.db.table("call_log")
            .insert(call_log_entry.model_dump(exclude_unset=True))
            .execute()
        )
        logger.debug(f"Call log entry creation result: {data}") # Add this
        return CallLogEntry.model_validate(data.data[0])

    async def update_call_log_entry(self, call_log_id: str, updates: dict) -> Optional[CallLogEntry]:
        data = (
            self.db.table("call_log")
            .update(updates)
            .eq("id", call_log_id)
            .execute()
        )
        if not data.data:
            return None # Or raise HTTPException if you want to signal not found
        return CallLogEntry.model_validate(data.data[0])

    async def get_call_log_entry(self, call_log_id: str) -> Optional[CallLogEntry]:
        data = self.db.table("call_log").select("*").eq("id", call_log_id).execute()
        if not data.data:
            return None # Or raise HTTPException if you want to signal not found
        return CallLogEntry.model_validate(data.data[0])