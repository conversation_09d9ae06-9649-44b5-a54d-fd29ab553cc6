# config.py
"""Configuration management module for server components."""

import os
from typing import TypedDict, Literal, NotRequired
from dotenv import load_dotenv

class DailyConfig(TypedDict):
    api_key: str
    api_url: str
    room_url: NotRequired[str]

BotType = Literal["simple", "flow"]

class AppConfig:
    def __init__(self):
        load_dotenv()

        # Validate required vars
        required = {
            "DAILY_API_KEY": os.getenv("DAILY_API_KEY"),
            "DEEPGRAM_API_KEY": os.getenv("DEEPGRAM_API_KEY"),
            "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
            "DAILY_API_URL": os.getenv("DAILY_API_URL"),
            "SUPABASE_URL": os.getenv("SUPABASE_URL"),
            "SUPABASE_KEY": os.getenv("SUPABASE_KEY"),
        }

        missing = [k for k, v in required.items() if not v]
        if missing:
            raise ValueError(f"Missing required env vars: {', '.join(missing)}")

        self.daily: DailyConfig = {
            "api_key": required["DAILY_API_KEY"],
            "api_url": required["DAILY_API_URL"],
        }

        # Add room_url only if it's provided
        if room_url := os.getenv("DAILY_SAMPLE_ROOM_URL"):
            self.daily["room_url"] = room_url

        # Server configuration
        self._bot_type: BotType = os.getenv("BOT_TYPE", "simple")
        if self._bot_type not in ("simple", "flow"):
            self._bot_type = "simple"  # Default to flow bot if invalid value

        # API Keys (loaded from .env)
        self.deepgram_api_key = os.getenv("DEEPGRAM_API_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        self.elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")
        self.cartesia_api_key = os.getenv("CARTESIA_API_KEY")  # For CARTESIA TTS
        self.azure_api_key = os.getenv("AZURE_API_KEY")  # For Azure TTS
        self.azure_region = os.getenv("AZURE_REGION")  # For Azure TTS
        self.whisper_model_size = os.getenv("WHISPER_MODEL_SIZE")
        self.groq_api_key = os.getenv("GROQ_API_KEY")
        self.deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
        self.google_api_key = os.getenv("GOOGLE_API_KEY")

    @property
    def bot_type(self) -> BotType:
        return self._bot_type

    @bot_type.setter
    def bot_type(self, value: BotType):
        if value not in ("simple", "flow"):
            raise ValueError("Bot type must be either 'simple' or 'flow'")
        self._bot_type = value