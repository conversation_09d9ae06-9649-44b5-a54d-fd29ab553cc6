from pipecat.transports.services.daily import DailyTransport, DailyParams
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.audio.vad.vad_analyzer import VADParams
from .config import AppConfig
from .daily_transport_wrapper import DailyTransportWrapper
from loguru import logger

class TransportFactory:
    def __init__(self, config: AppConfig):
        default_vad_params = VADParams(
            start_secs=0.3,
            stop_secs=0.3,
            threshold=0.6,
            min_speech_duration_ms=300,
            min_silence_duration_ms=150
        )

        logger.debug(f"TransportFactory - default_vad_params instance: {default_vad_params}")

        self._default_params = DailyParams(
            audio_out_enabled=True,
            vad_enabled=True,
            vad_analyzer=SileroVADAnalyzer(params=default_vad_params),
            vad_audio_passthrough=True,
        )
        logger.debug("TransportFactory initialized with default DailyParams and VADParams.")
        logger.debug(f"TransportFactory - default_params.vad_analyzer type: {type(self._default_params.vad_analyzer)}")
        logger.debug(f"TransportFactory - default_params.vad_analyzer.params type: {type(self._default_params.vad_analyzer.params)}")


    def create_flow_assistant_transport(self, room_url: str, token: str, vad_params: VADParams = None):
        params = self._default_params.copy()
        if vad_params:
            logger.debug("VAD params provided, updating DailyParams and VAD analyzer for Flow Assistant Transport.")
            params.vad_analyzer = SileroVADAnalyzer(params=vad_params)
        else:
            logger.debug("No VAD params provided, using default VAD params for Flow Assistant Transport.")

        transport = DailyTransport(
            room_url, token, "Lead Qualification Bot", params
        )
        logger.info(f"Flow Assistant DailyTransport created for room: {room_url}")
        return DailyTransportWrapper(transport)

    def create_simple_assistant_transport(self, room_url: str, token: str, vad_params: VADParams = None):
        logger.debug(f"create_simple_assistant_transport - type(vad_params): {type(vad_params)}") # ADD THIS LOG
        params = self._default_params.copy()
        logger.debug(f"create_simple_assistant_transport - params.vad_analyzer type BEFORE override: {type(params.vad_analyzer)}")
        if vad_params:
            logger.debug("VAD params provided, updating DailyParams and VAD analyzer for Simple Assistant Transport.")
            params.vad_analyzer = SileroVADAnalyzer(params=vad_params)
        else:
            logger.debug("No VAD params provided, using default VAD params for Simple Assistant Transport.")
        logger.debug(f"create_simple_assistant_transport - params.vad_analyzer type AFTER override (or default): {type(params.vad_analyzer)}")
        transport = DailyTransport(
            room_url, token, "Voice Assistant", params
        )
        logger.info(f"Simple Assistant DailyTransport created for room: {room_url}")
        return DailyTransportWrapper(transport)