# utils/agent_service.py
from typing import List
from fastapi import HTTPException
from supabase import Client

from utils.agent_model import Agent

# --- Agent Data Service ---
class AgentService:
    def __init__(self, supabase_client: Client):
        self.db = supabase_client

    async def create_agent(self, agent: Agent) -> Agent:
        """Create a new agent"""
        # Prepare data for database insertion
        agent_data = agent.model_dump(exclude_unset=True)

        # Ensure required fields with defaults are included
        if 'status' not in agent_data:
            agent_data['status'] = agent.status.value if hasattr(agent.status, 'value') else agent.status
        if 'category' not in agent_data:
            agent_data['category'] = agent.category

        # Ensure all config objects are properly serialized
        for config_field in ['stt_config', 'llm_config', 'tts_config', 'vad_config', 'conversation_config']:
            if config_field in agent_data and agent_data[config_field] is not None:
                if hasattr(agent_data[config_field], 'model_dump'):
                    agent_data[config_field] = agent_data[config_field].model_dump()
                elif hasattr(agent_data[config_field], 'dict'):
                    agent_data[config_field] = agent_data[config_field].dict()

        data = self.db.table("agents").insert(agent_data).execute()
        return Agent.model_validate(data.data[0])

    async def get_agent(self, agent_id: str) -> Agent:
        data = self.db.table("agents").select("*").eq("id", agent_id).execute()
        if not data.data:
            raise HTTPException(status_code=404, detail="Agent not found")
        return Agent.model_validate(data.data[0])

    async def get_all_agents(self) -> List[Agent]:
        data = self.db.table("agents").select("*").execute()
        return [Agent.model_validate(item) for item in data.data]

    async def update_agent(self, agent_id: str, agent: Agent) -> Agent:
        """Update an existing agent"""
        # Prepare data for database update
        agent_data = agent.model_dump(exclude_unset=True)

        # Ensure all config objects are properly serialized
        for config_field in ['stt_config', 'llm_config', 'tts_config', 'vad_config', 'conversation_config']:
            if config_field in agent_data and agent_data[config_field] is not None:
                if hasattr(agent_data[config_field], 'model_dump'):
                    agent_data[config_field] = agent_data[config_field].model_dump()
                elif hasattr(agent_data[config_field], 'dict'):
                    agent_data[config_field] = agent_data[config_field].dict()

        data = (
            self.db.table("agents")
            .update(agent_data)
            .eq("id", agent_id)
            .execute()
        )
        if not data.data:
            raise HTTPException(status_code=404, detail="Agent not found")
        return Agent.model_validate(data.data[0])

    async def delete_agent(self, agent_id: str) -> None:
        data = self.db.table("agents").delete().eq("id", agent_id).execute()
        if not data.data:
            raise HTTPException(status_code=404, detail="Agent not found")