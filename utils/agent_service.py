# utils/agent_service.py
from typing import List
from fastapi import HTTPEx<PERSON>
from supabase import Client

from utils.agent_model import Agent

# --- Agent Data Service ---
class AgentService:
    def __init__(self, supabase_client: Client):
        self.db = supabase_client

    async def create_agent(self, agent: Agent) -> Agent:
        data = self.db.table("agents").insert(agent.model_dump(exclude_unset=True)).execute()
        return Agent.model_validate(data.data[0])

    async def get_agent(self, agent_id: str) -> Agent:
        data = self.db.table("agents").select("*").eq("id", agent_id).execute()
        if not data.data:
            raise HTTPException(status_code=404, detail="Agent not found")
        return Agent.model_validate(data.data[0])

    async def get_all_agents(self) -> List[Agent]:
        data = self.db.table("agents").select("*").execute()
        return [Agent.model_validate(item) for item in data.data]

    async def update_agent(self, agent_id: str, agent: Agent) -> Agent:
        data = (
            self.db.table("agents")
            .update(agent.model_dump(exclude_unset=True))
            .eq("id", agent_id)
            .execute()
        )
        if not data.data:
            raise HTTPException(status_code=404, detail="Agent not found")
        return Agent.model_validate(data.data[0])

    async def delete_agent(self, agent_id: str) -> None:
        data = self.db.table("agents").delete().eq("id", agent_id).execute()
        if not data.data:
            raise HTTPException(status_code=404, detail="Agent not found")