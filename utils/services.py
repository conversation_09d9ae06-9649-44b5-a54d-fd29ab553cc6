# utils/services.py
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

# Core Pipecat services
from pipecat.services.deepgram import DeepgramSTTService, DeepgramTTSService
from pipecat.services.openai import OpenAILLMService, OpenAITTSService
from pipecat.services.whisper import WhisperSTTService
from pipecat.services.anthropic import AnthropicLLMService
from pipecat.services.elevenlabs import ElevenLabsTTSService
from pipecat.services.google import GoogleLLMService
from pipecat.services.cartesia import CartesiaTTSService
from pipecat.services.azure import AzureTTSService, AzureSTTService
from pipecat.services.deepseek import DeepSeekLLMService
from pipecat.services.groq import GroqLLMService

# Additional services for enhanced capabilities
try:
    from pipecat.services.assemblyai import AssemblyAISTTService
except (ImportError, Exception):
    AssemblyAISTTService = None

try:
    from pipecat.services.together import TogetherLLMService
except (ImportError, Exception):
    TogetherLLMService = None

try:
    from pipecat.services.fireworks import FireworksLLMService
except (ImportError, Exception):
    FireworksLLMService = None

try:
    from pipecat.services.ollama import OllamaLLMService
except (ImportError, Exception):
    OllamaLLMService = None

try:
    from pipecat.services.playht import PlayHTTTSService
except (ImportError, Exception):
    PlayHTTTSService = None

try:
    from pipecat.services.lmnt import LMNTTTSService
except (ImportError, Exception):
    LMNTTTSService = None

from loguru import logger
from utils.agent_model import STTConfig, LLMConfig, TTSConfig, VADConfig, ConversationConfig

@dataclass
class ServiceRegistry:
    """Singleton registry for managing shared services across bots."""

    _instance: Optional["ServiceRegistry"] = None

    stt: Optional[object] = None
    tts: Optional[object] = None
    llm: Optional[object] = None
    _stt_callback: Optional[Callable[[str], None]] = None

    def __new__(cls, config, agent_config=None):
        if cls._instance is None:
            cls._instance = super(ServiceRegistry, cls).__new__(cls)
            # Initialize services only once
            cls._instance.stt = None
            cls._instance.tts = None
            cls._instance.llm = None
            cls._instance._stt_callback = None
        return cls._instance

    def initialize_services(self, config, agent_config=None):
        """Initialize core services based on agent configuration or defaults."""
        if agent_config:
            # Handle both dict and Agent object
            if hasattr(agent_config, '__dict__'):
                # Agent object
                stt_service_name = getattr(agent_config, 'stt_service', None)
                stt_config = getattr(agent_config, 'stt_config', None)
                llm_service_name = getattr(agent_config, 'llm_service', None)
                llm_config = getattr(agent_config, 'llm_config', None)
                tts_service_name = getattr(agent_config, 'tts_service', None)
                tts_config = getattr(agent_config, 'tts_config', None)
                vad_config = getattr(agent_config, 'vad_config', None)
                conversation_config = getattr(agent_config, 'conversation_config', None)
            else:
                # Dict format (legacy)
                stt_service_name = agent_config.get("stt_service")
                stt_config = agent_config.get("stt_config", {})
                llm_service_name = agent_config.get("llm_service")
                llm_config = agent_config.get("llm_config", {})
                tts_service_name = agent_config.get("tts_service")
                tts_config = agent_config.get("tts_config", {})
                vad_config = agent_config.get("vad_config", {})
                conversation_config = agent_config.get("conversation_config", {})
        else:
            # Default values
            stt_service_name = None
            stt_config = {}
            llm_service_name = None
            llm_config = {}
            tts_service_name = None
            tts_config = {}
            vad_config = {}
            conversation_config = {}

        # Convert Pydantic models to dicts if needed
        if hasattr(stt_config, 'dict'):
            stt_config = stt_config.dict()
        if hasattr(llm_config, 'dict'):
            llm_config = llm_config.dict()
        if hasattr(tts_config, 'dict'):
            tts_config = tts_config.dict()
        if hasattr(vad_config, 'dict'):
            vad_config = vad_config.dict()
        if hasattr(conversation_config, 'dict'):
            conversation_config = conversation_config.dict()

        # Only initialize services if they haven't been initialized yet
        if self.stt is None:
            self.stt = self.get_stt_service(config, stt_service_name, stt_config or {})
        if self.tts is None:
            self.tts = self.get_tts_service(config, tts_service_name, tts_config or {})
        if self.llm is None:
            self.llm = self.get_llm_service(config, llm_service_name, llm_config or {})

        if self.stt and self._stt_callback:
            self.stt.set_callback(self._stt_callback)



    def get_stt_service(self, config, service_name, service_config):
        """Returns STT service based on service_name with enhanced configuration support."""
        service_config = service_config or {}

        if service_name == "deepgram":
            # Enhanced Deepgram configuration
            model = service_config.get("model", "nova-2")
            language = service_config.get("language", "en-US")
            smart_format = service_config.get("smart_format", True)
            punctuate = service_config.get("punctuate", True)
            interim_results = service_config.get("interim_results", True)

            logger.info(f"Initializing Deepgram STT Service: model={model}, language={language}")

            # Build Deepgram options
            options = {
                "model": model,
                "language": language,
                "smart_format": smart_format,
                "punctuate": punctuate,
                "interim_results": interim_results
            }

            # Add optional parameters if specified
            if "profanity_filter" in service_config:
                options["profanity_filter"] = service_config["profanity_filter"]
            if "vad_events" in service_config:
                options["vad_events"] = service_config["vad_events"]

            return DeepgramSTTService(api_key=config.deepgram_api_key, **options)

        elif service_name in ["whisper", "openai_whisper"]:
            # Enhanced OpenAI Whisper configuration
            model = service_config.get("model", "whisper-1")
            language = service_config.get("language", "en")
            temperature = service_config.get("temperature", 0.0)

            logger.info(f"Initializing OpenAI Whisper STT Service: model={model}, language={language}")

            if hasattr(config, 'openai_api_key') and config.openai_api_key:
                # Use OpenAI API if available
                return WhisperSTTService(
                    api_key=config.openai_api_key,
                    model=model,
                    language=language,
                    temperature=temperature
                )
            else:
                # Fallback to local Whisper
                model_size = service_config.get("model_size", "base")
                return WhisperSTTService(model_size=model_size)

        elif service_name == "azure":
            # Azure STT configuration
            language = service_config.get("language", "en-US")

            if not hasattr(config, 'azure_api_key') or not config.azure_api_key:
                raise ValueError("AZURE_API_KEY environment variable is not set for Azure STT.")

            logger.info(f"Initializing Azure STT Service: language={language}")
            return AzureSTTService(
                api_key=config.azure_api_key,
                region=getattr(config, 'azure_region', 'eastus'),
                language=language
            )

        elif service_name == "assemblyai" and AssemblyAISTTService:
            # AssemblyAI STT configuration
            model = service_config.get("model", "best")
            language_detection = service_config.get("language_detection", False)
            speaker_labels = service_config.get("speaker_labels", False)
            sentiment_analysis = service_config.get("sentiment_analysis", False)

            if not hasattr(config, 'assemblyai_api_key') or not config.assemblyai_api_key:
                raise ValueError("ASSEMBLYAI_API_KEY environment variable is not set for AssemblyAI STT.")

            logger.info(f"Initializing AssemblyAI STT Service: model={model}")
            return AssemblyAISTTService(
                api_key=config.assemblyai_api_key,
                model=model,
                language_detection=language_detection,
                speaker_labels=speaker_labels,
                sentiment_analysis=sentiment_analysis
            )

        elif service_name == "groq":
            # Groq Whisper configuration
            model = service_config.get("model", "whisper-large-v3")
            language = service_config.get("language", "en")
            temperature = service_config.get("temperature", 0.0)

            if not hasattr(config, 'groq_api_key') or not config.groq_api_key:
                raise ValueError("GROQ_API_KEY environment variable is not set for Groq STT.")

            logger.info(f"Initializing Groq STT Service: model={model}")
            # Note: This would need to be implemented based on Groq's actual STT API
            # For now, fallback to Whisper with Groq configuration
            return WhisperSTTService(model_size="large")

        else:
            logger.warning(f"STT Service '{service_name}' not recognized or not configured. Defaulting to Deepgram STT.")
            return DeepgramSTTService(api_key=config.deepgram_api_key)

    def get_llm_service(self, config, service_name, service_config):
        """Returns LLM service based on service_name with enhanced configuration support."""
        service_config = service_config or {}

        if service_name == "openai":
            # Enhanced OpenAI configuration
            model = service_config.get("model", "gpt-4o-mini")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_tokens", 1000)
            top_p = service_config.get("top_p", 1.0)
            frequency_penalty = service_config.get("frequency_penalty", 0.0)
            presence_penalty = service_config.get("presence_penalty", 0.0)

            logger.info(f"Initializing OpenAI LLM Service: model={model}, temp={temperature}")

            return OpenAILLMService(
                api_key=config.openai_api_key,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                frequency_penalty=frequency_penalty,
                presence_penalty=presence_penalty
            )

        elif service_name == "anthropic":
            # Enhanced Anthropic configuration
            model = service_config.get("model", "claude-3-5-haiku-latest")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_tokens", 1000)
            top_p = service_config.get("top_p", 1.0)
            top_k = service_config.get("top_k", 40)

            logger.info(f"Initializing Anthropic LLM Service: model={model}, temp={temperature}")

            return AnthropicLLMService(
                api_key=config.anthropic_api_key,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                top_k=top_k
            )

        elif service_name == "groq":
            # Enhanced Groq configuration
            model = service_config.get("model", "llama-3.3-70b-versatile")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_tokens", 1000)
            top_p = service_config.get("top_p", 1.0)

            logger.info(f"Initializing Groq LLM Service: model={model}, temp={temperature}")

            return GroqLLMService(
                api_key=config.groq_api_key,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p
            )

        elif service_name == "deepseek":
            # Enhanced DeepSeek configuration
            model = service_config.get("model", "deepseek-chat")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_tokens", 1000)
            top_p = service_config.get("top_p", 1.0)
            frequency_penalty = service_config.get("frequency_penalty", 0.0)

            logger.info(f"Initializing DeepSeek LLM Service: model={model}, temp={temperature}")

            return DeepSeekLLMService(
                api_key=config.deepseek_api_key,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                frequency_penalty=frequency_penalty
            )

        elif service_name == "google":
            # Enhanced Google configuration
            model = service_config.get("model", "gemini-2.0-flash-001")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_output_tokens", 1000)
            top_p = service_config.get("top_p", 1.0)
            top_k = service_config.get("top_k", 40)

            logger.info(f"Initializing Google LLM Service: model={model}, temp={temperature}")

            return GoogleLLMService(
                api_key=config.google_api_key,
                model=model,
                temperature=temperature,
                max_output_tokens=max_tokens,
                top_p=top_p,
                top_k=top_k
            )

        elif service_name == "together" and TogetherLLMService:
            # Together AI configuration
            model = service_config.get("model", "meta-llama/Llama-3.2-3B-Instruct-Turbo")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_tokens", 1000)
            top_p = service_config.get("top_p", 1.0)

            if not hasattr(config, 'together_api_key') or not config.together_api_key:
                raise ValueError("TOGETHER_API_KEY environment variable is not set for Together AI.")

            logger.info(f"Initializing Together AI LLM Service: model={model}")

            return TogetherLLMService(
                api_key=config.together_api_key,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p
            )

        elif service_name == "fireworks" and FireworksLLMService:
            # Fireworks AI configuration
            model = service_config.get("model", "accounts/fireworks/models/llama-v3p1-8b-instruct")
            temperature = service_config.get("temperature", 0.7)
            max_tokens = service_config.get("max_tokens", 1000)

            if not hasattr(config, 'fireworks_api_key') or not config.fireworks_api_key:
                raise ValueError("FIREWORKS_API_KEY environment variable is not set for Fireworks AI.")

            logger.info(f"Initializing Fireworks AI LLM Service: model={model}")

            return FireworksLLMService(
                api_key=config.fireworks_api_key,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )

        elif service_name == "ollama" and OllamaLLMService:
            # Ollama configuration
            model = service_config.get("model", "llama3.2")
            base_url = service_config.get("base_url", "http://localhost:11434")
            temperature = service_config.get("temperature", 0.7)

            logger.info(f"Initializing Ollama LLM Service: model={model}, base_url={base_url}")

            return OllamaLLMService(
                model=model,
                base_url=base_url,
                temperature=temperature
            )

        else:
            logger.warning(f"LLM Service '{service_name}' not recognized or not configured. Defaulting to OpenAI LLM (gpt-4o-mini).")
            return OpenAILLMService(api_key=config.openai_api_key, model="gpt-4o-mini")

    def get_tts_service(self, config, service_name, service_config):
        """
        Initializes and returns a TTS service based on the provided configuration with enhanced support.
        Defaults to Deepgram TTS if service_name is None or invalid.
        """
        service_config = service_config or {}

        if service_name == "deepgram":
            # Enhanced Deepgram TTS configuration
            model = service_config.get("model", "aura-asteria-en")
            encoding = service_config.get("encoding", "linear16")
            sample_rate = service_config.get("sample_rate", 24000)

            logger.info(f"Initializing Deepgram TTS: model={model}, encoding={encoding}")

            return DeepgramTTSService(
                api_key=config.deepgram_api_key,
                model=model,
                encoding=encoding,
                sample_rate=sample_rate
            )

        elif service_name == "elevenlabs":
            # Enhanced ElevenLabs configuration
            voice_id = service_config.get("voice_id", "21m00Tcm4TlvDq8ikWAM")
            model = service_config.get("model", "eleven_flash_v2_5")
            stability = service_config.get("stability", 0.5)
            similarity_boost = service_config.get("similarity_boost", 0.8)
            style = service_config.get("style", 0.0)
            use_speaker_boost = service_config.get("use_speaker_boost", True)
            speed = service_config.get("speed", 1.0)

            if not voice_id:
                raise ValueError("voice_id is required for ElevenLabs TTS in agent configuration.")

            # Build voice settings
            voice_settings = {
                "stability": stability,
                "similarity_boost": similarity_boost,
                "style": style,
                "use_speaker_boost": use_speaker_boost
            }

            logger.info(f"Initializing ElevenLabs TTS: voice_id={voice_id}, model={model}")

            return ElevenLabsTTSService(
                api_key=config.elevenlabs_api_key,
                voice_id=voice_id,
                model_id=model,
                voice_settings=voice_settings,
                speed=speed
            )

        elif service_name == "openai":
            # OpenAI TTS configuration
            model = service_config.get("model", "tts-1")
            voice = service_config.get("voice", "alloy")
            speed = service_config.get("speed", 1.0)
            response_format = service_config.get("response_format", "mp3")

            logger.info(f"Initializing OpenAI TTS: model={model}, voice={voice}")

            return OpenAITTSService(
                api_key=config.openai_api_key,
                model=model,
                voice=voice,
                speed=speed,
                response_format=response_format
            )

        elif service_name == "cartesia":
            # Enhanced Cartesia configuration
            voice_id = service_config.get("voice_id", "a0e99841-438c-4a64-b679-ae501e7d6091")
            model = service_config.get("model", "sonic-english")
            speed = service_config.get("speed", 1.0)
            emotion = service_config.get("emotion", "neutral")

            if not hasattr(config, 'cartesia_api_key') or not config.cartesia_api_key:
                raise ValueError("CARTESIA_API_KEY environment variable is not set for Cartesia TTS.")

            logger.info(f"Initializing Cartesia TTS: voice_id={voice_id}, model={model}")

            return CartesiaTTSService(
                api_key=config.cartesia_api_key,
                voice_id=voice_id,
                model=model,
                speed=speed,
                emotion=emotion
            )

        elif service_name == "azure":
            # Enhanced Azure TTS configuration
            voice_name = service_config.get("voice_name", "en-US-JennyNeural")
            speaking_rate = service_config.get("speaking_rate", 1.0)
            speaking_volume = service_config.get("speaking_volume", 1.0)
            pitch = service_config.get("pitch", "medium")

            if not hasattr(config, 'azure_api_key') or not config.azure_api_key:
                raise ValueError("AZURE_API_KEY and AZURE_REGION environment variables must be set for Azure TTS.")

            logger.info(f"Initializing Azure TTS: voice_name={voice_name}, region={getattr(config, 'azure_region', 'eastus')}")

            return AzureTTSService(
                api_key=config.azure_api_key,
                region=getattr(config, 'azure_region', 'eastus'),
                voice_name=voice_name,
                speaking_rate=speaking_rate,
                speaking_volume=speaking_volume,
                pitch=pitch
            )

        elif service_name == "playht" and PlayHTTTSService:
            # PlayHT configuration
            voice_id = service_config.get("voice_id")
            quality = service_config.get("quality", "medium")
            speed = service_config.get("speed", 1.0)
            seed = service_config.get("seed")

            if not voice_id:
                raise ValueError("voice_id is required for PlayHT TTS.")
            if not hasattr(config, 'playht_api_key') or not config.playht_api_key:
                raise ValueError("PLAYHT_API_KEY environment variable is not set for PlayHT TTS.")

            logger.info(f"Initializing PlayHT TTS: voice_id={voice_id}, quality={quality}")

            return PlayHTTTSService(
                api_key=config.playht_api_key,
                voice_id=voice_id,
                quality=quality,
                speed=speed,
                seed=seed
            )

        elif service_name == "lmnt" and LMNTTTSService:
            # LMNT configuration
            voice_id = service_config.get("voice_id")
            speed = service_config.get("speed", 1.0)
            length = service_config.get("length", "medium")
            format = service_config.get("format", "mp3")

            if not voice_id:
                raise ValueError("voice_id is required for LMNT TTS.")
            if not hasattr(config, 'lmnt_api_key') or not config.lmnt_api_key:
                raise ValueError("LMNT_API_KEY environment variable is not set for LMNT TTS.")

            logger.info(f"Initializing LMNT TTS: voice_id={voice_id}, speed={speed}")

            return LMNTTTSService(
                api_key=config.lmnt_api_key,
                voice_id=voice_id,
                speed=speed,
                length=length,
                format=format
            )

        else:
            logger.warning(f"TTS Service '{service_name}' not recognized or not configured. Defaulting to Deepgram TTS.")
            return DeepgramTTSService(api_key=config.deepgram_api_key)

    def set_callback(self, callback_type, callback):
        """Sets a callback function for a service (stt, llm)."""
        if callback_type == "stt_transcript":
            self._stt_callback = callback
            if self.stt:
                self.stt.set_callback(callback)
        elif callback_type == "llm":
            if self.llm:
                self.llm.set_callback(callback)
            else:
                logger.warning("LLM service not initialized, cannot set callback.")
        elif callback_type == "last_message_time":
            if self.stt:
                self.stt.set_callback(callback)
            if self.llm:
                self.llm.set_callback(callback)
        else:
            logger.warning(f"Unknown callback type '{callback_type}', cannot set callback.")