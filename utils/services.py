# utils/services.py
from typing import Optional, Callable
from dataclasses import dataclass

from pipecat.services.deepgram import DeepgramSTTService, DeepgramTTSService
from pipecat.services.openai import OpenAILLMService
from pipecat.services.whisper import WhisperSTTService
from pipecat.services.anthropic import AnthropicLLMService
from pipecat.services.elevenlabs import ElevenLabsTTSService
from pipecat.services.google import GoogleLLMService
from pipecat.services.cartesia import CartesiaTTSService  # AssemblyAI TTS
from pipecat.services.azure import AzureTTSService  # Azure TTS
from pipecat.services.deepseek import DeepSeekLLMService  # Deepseek LLM
from pipecat.services.groq import GroqLLMService  # Groq LLM
from loguru import logger

@dataclass
class ServiceRegistry:
    """Singleton registry for managing shared services across bots."""

    _instance: Optional["ServiceRegistry"] = None

    stt: Optional[object] = None
    tts: Optional[object] = None
    llm: Optional[object] = None
    _stt_callback: Optional[Callable[[str], None]] = None

    def __new__(cls, config, agent_config=None):
        if cls._instance is None:
            cls._instance = super(ServiceRegistry, cls).__new__(cls)
            # Initialize services only once
            cls._instance.stt = None
            cls._instance.tts = None
            cls._instance.llm = None
            cls._instance._stt_callback = None
        return cls._instance

    def initialize_services(self, config, agent_config=None):
        """Initialize core services based on agent configuration or defaults."""
        stt_service_name = agent_config.get("stt_service") if agent_config else None
        stt_config = agent_config.get("stt_config") if agent_config else {}
        llm_service_name = agent_config.get("llm_service") if agent_config else None
        llm_config = agent_config.get("llm_config") if agent_config else {}
        tts_service_name = agent_config.get("tts_service") if agent_config else None
        tts_config = agent_config.get("tts_config") if agent_config else {}

        # Only initialize services if they haven't been initialized yet
        if self.stt is None:
            self.stt = self.get_stt_service(config, stt_service_name, stt_config)
        if self.tts is None:
            self.tts = self.get_tts_service(config, tts_service_name, tts_config)
        if self.llm is None:
            self.llm = self.get_llm_service(config, llm_service_name, llm_config)

        if self.stt and self._stt_callback:
            self.stt.set_callback(self._stt_callback)



    def get_stt_service(self, config, service_name, service_config):
        """Returns STT service based on service_name, defaults to Deepgram if None or invalid."""
        if service_name == "deepgram":
            logger.info("Initializing Deepgram STT Service.")
            return DeepgramSTTService(api_key=config.deepgram_api_key)
        elif service_name == "whisper":
            model_size = service_config.get("model_size") or config.whisper_model_size or "base"
            logger.info(f"Initializing Whisper STT Service with model size: {model_size}")
            return WhisperSTTService(model_size=model_size)
        else:
            logger.warning(f"STT Service '{service_name}' not recognized or not configured. Defaulting to Deepgram STT.")
            return DeepgramSTTService(api_key=config.deepgram_api_key)

    def get_llm_service(self, config, service_name, service_config):
        """Returns LLM service based on service_name, defaults to OpenAI if None or invalid."""
        if service_name == "openai":
            model = service_config.get("model", "gpt-4o-mini")
            logger.info(f"Initializing OpenAI LLM Service with model: {model}")
            return OpenAILLMService(
                api_key=config.openai_api_key,
                model=model
            )
        elif service_name == "anthropic":
            model = service_config.get("model", "claude-3-5-haiku-latest")
            logger.info(f"Initializing Anthropic LLM Service with model: {model}")
            return AnthropicLLMService(
                api_key=config.anthropic_api_key,
                model=model
            )
        elif service_name == "groq":
            model = service_config.get("model", "llama-3.3-70b-versatile")
            logger.info(f"Initializing Groq LLM Service with model: {model}")
            return GroqLLMService(
                api_key=config.groq_api_key,
                model=model
            )
        elif service_name == "deepseek":
            model = service_config.get("model", "deepseek-chat")
            logger.info(f"Initializing DeepSeek LLM Service with model: {model}")
            return DeepSeekLLMService(
                api_key=config.deepseek_api_key,
                model=model
            )
        elif service_name == "google":
            model = service_config.get("model", "gemini-2.0-flash-001")
            logger.info(f"Initializing Google LLM Service with model: {model}")
            return GoogleLLMService(
                api_key=config.google_api_key,
                model=model
            )
        else:
            logger.warning(f"LLM Service '{service_name}' not recognized or not configured. Defaulting to OpenAI LLM (gpt-4o-mini).")
            return OpenAILLMService(api_key=config.openai_api_key, model="gpt-4o-mini")  # Default to OpenAI

    def get_tts_service(self, config, service_name, service_config):
        """
        Initializes and returns a TTS service based on the provided configuration.
        Defaults to Deepgram TTS if service_name is None or invalid.
        """
        if service_name == "deepgram":
            model = service_config.get("model", "aura-asteria-en")
            logger.info(f"Using Deepgram TTS: model={model}")
            return DeepgramTTSService(api_key=config.deepgram_api_key, model=model)

        elif service_name == "elevenlabs":
            voice_id = service_config.get("voice_id")
            model_id = service_config.get("model_id", "eleven_turbo_v2")
            voice_settings = service_config.get("voice_settings")

            if not voice_id:
                raise ValueError("voice_id is required for ElevenLabs TTS in agent configuration or environment variables.")

            logger.info(f"Using ElevenLabs TTS: voice_id={voice_id}, model_id={model_id}, voice_settings={voice_settings}")
            return ElevenLabsTTSService(
                api_key=config.elevenlabs_api_key,
                voice_id=voice_id,
                model_id=model_id,
                voice_settings=voice_settings,
            )

        elif service_name == "cartesia":
            voice_id = service_config.get("voice_id", "156fb8d2-335b-4950-9cb3-a2d33befec77")

            if not config.cartesia_api_key:
                raise ValueError("CARTESIA_API_KEY environment variable is not set for Cartesia TTS.")

            logger.info(f"Using Cartesia TTS: voice_id={voice_id}")
            return CartesiaTTSService(api_key=config.cartesia_api_key, voice_id=voice_id)

        elif service_name == "azure":
            voice_name = service_config.get("voice_name", "en-US-JennyNeural")

            if not config.azure_api_key or not config.azure_region:
                raise ValueError("AZURE_API_KEY and AZURE_REGION environment variables must be set for Azure TTS.")

            logger.info(f"Using Azure TTS: voice_name={voice_name}, region={config.azure_region}")
            return AzureTTSService(
                api_key=config.azure_api_key,
                region=config.azure_region,
                voice_name=voice_name,
            )

        else:
            logger.warning(f"TTS Service '{service_name}' not recognized or not configured. Defaulting to Deepgram TTS.")
            return DeepgramTTSService(api_key=config.deepgram_api_key)  # Default to Deepgram

    def set_callback(self, callback_type, callback):
        """Sets a callback function for a service (stt, llm)."""
        if callback_type == "stt_transcript":
            self._stt_callback = callback
            if self.stt:
                self.stt.set_callback(callback)
        elif callback_type == "llm":
            if self.llm:
                self.llm.set_callback(callback)
            else:
                logger.warning("LLM service not initialized, cannot set callback.")
        elif callback_type == "last_message_time":
            if self.stt:
                self.stt.set_callback(callback)
            if self.llm:
                self.llm.set_callback(callback)
        else:
            logger.warning(f"Unknown callback type '{callback_type}', cannot set callback.")