# utils/service_capabilities.py
from typing import Dict, List, Any
from utils.agent_model import ServiceCapability, ServiceConfigurationTemplate

# Service capability definitions based on Pipecat documentation
SERVICE_CAPABILITIES: Dict[str, ServiceCapability] = {
    # STT Services
    "deepgram": ServiceCapability(
        service_type="stt",
        service_name="deepgram",
        display_name="Deepgram",
        description="High-accuracy real-time speech-to-text with advanced features",
        supported_languages=["en", "en-US", "en-GB", "en-AU", "es", "fr", "de", "it", "pt", "ja", "ko", "zh", "hi", "ru"],
        supported_models=["nova-2", "nova-2-general", "nova-2-meeting", "nova-2-phonecall", "nova-2-finance", "nova-2-conversationalai", "nova-2-voicemail", "nova-2-video", "nova-2-medical", "nova-2-drivethru", "nova-2-automotive"],
        features=["real_time", "interim_results", "smart_format", "punctuation", "profanity_filter", "vad_events", "speaker_detection", "language_detection"],
        pricing_tier="paid",
        latency_rating="low",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "model": {"type": "string", "enum": ["nova-2", "nova-2-general", "nova-2-meeting"], "default": "nova-2"},
                "language": {"type": "string", "default": "en-US"},
                "smart_format": {"type": "boolean", "default": True},
                "punctuate": {"type": "boolean", "default": True},
                "profanity_filter": {"type": "boolean", "default": True},
                "interim_results": {"type": "boolean", "default": True},
                "vad_events": {"type": "boolean", "default": False}
            }
        }
    ),
    
    "openai_whisper": ServiceCapability(
        service_type="stt",
        service_name="openai_whisper",
        display_name="OpenAI Whisper",
        description="OpenAI's robust speech recognition model",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", "ar", "hi", "tr"],
        supported_models=["whisper-1"],
        features=["multilingual", "translation", "timestamps"],
        pricing_tier="paid",
        latency_rating="medium",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "model": {"type": "string", "enum": ["whisper-1"], "default": "whisper-1"},
                "language": {"type": "string", "default": "en"},
                "temperature": {"type": "number", "minimum": 0, "maximum": 1, "default": 0},
                "response_format": {"type": "string", "enum": ["json", "text", "srt", "verbose_json", "vtt"], "default": "json"}
            }
        }
    ),
    
    "assemblyai": ServiceCapability(
        service_type="stt",
        service_name="assemblyai",
        display_name="AssemblyAI",
        description="AI-powered speech recognition with advanced analytics",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "nl", "hi", "ja"],
        supported_models=["best", "nano"],
        features=["speaker_diarization", "sentiment_analysis", "entity_detection", "content_safety", "auto_chapters"],
        pricing_tier="paid",
        latency_rating="medium",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "model": {"type": "string", "enum": ["best", "nano"], "default": "best"},
                "language_detection": {"type": "boolean", "default": False},
                "speaker_labels": {"type": "boolean", "default": False},
                "sentiment_analysis": {"type": "boolean", "default": False}
            }
        }
    ),
    
    # LLM Services
    "openai": ServiceCapability(
        service_type="llm",
        service_name="openai",
        display_name="OpenAI GPT",
        description="Advanced language models from OpenAI",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", "ar", "hi"],
        supported_models=["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"],
        features=["function_calling", "vision", "streaming", "json_mode", "reproducible_outputs"],
        pricing_tier="paid",
        latency_rating="low",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "model": {"type": "string", "enum": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo"], "default": "gpt-4o"},
                "temperature": {"type": "number", "minimum": 0, "maximum": 2, "default": 0.7},
                "max_tokens": {"type": "integer", "minimum": 1, "maximum": 8192, "default": 1000},
                "top_p": {"type": "number", "minimum": 0, "maximum": 1, "default": 1},
                "frequency_penalty": {"type": "number", "minimum": -2, "maximum": 2, "default": 0},
                "presence_penalty": {"type": "number", "minimum": -2, "maximum": 2, "default": 0},
                "system_prompt": {"type": "string", "default": "You are a helpful assistant."}
            },
            "required": ["model"]
        }
    ),
    
    "anthropic": ServiceCapability(
        service_type="llm",
        service_name="anthropic",
        display_name="Anthropic Claude",
        description="Constitutional AI models from Anthropic",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh"],
        supported_models=["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229"],
        features=["long_context", "function_calling", "vision", "streaming", "safety_focused"],
        pricing_tier="paid",
        latency_rating="low",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "model": {"type": "string", "enum": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022"], "default": "claude-3-5-sonnet-20241022"},
                "temperature": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.7},
                "max_tokens": {"type": "integer", "minimum": 1, "maximum": 8192, "default": 1000},
                "top_p": {"type": "number", "minimum": 0, "maximum": 1, "default": 1},
                "top_k": {"type": "integer", "minimum": 1, "default": 40},
                "system_prompt": {"type": "string", "default": "You are a helpful assistant."}
            },
            "required": ["model"]
        }
    ),
    
    # TTS Services
    "elevenlabs": ServiceCapability(
        service_type="tts",
        service_name="elevenlabs",
        display_name="ElevenLabs",
        description="High-quality AI voice synthesis with emotion and style control",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "pl", "hi", "ar", "zh", "ja", "ko"],
        supported_models=["eleven_flash_v2_5", "eleven_turbo_v2_5", "eleven_multilingual_v2"],
        features=["voice_cloning", "emotion_control", "style_control", "streaming", "multilingual"],
        pricing_tier="paid",
        latency_rating="low",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "voice_id": {"type": "string", "default": "21m00Tcm4TlvDq8ikWAM"},
                "model": {"type": "string", "enum": ["eleven_flash_v2_5", "eleven_turbo_v2_5"], "default": "eleven_flash_v2_5"},
                "stability": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.5},
                "similarity_boost": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.8},
                "style": {"type": "number", "minimum": 0, "maximum": 1, "default": 0},
                "use_speaker_boost": {"type": "boolean", "default": True},
                "speed": {"type": "number", "minimum": 0.25, "maximum": 4, "default": 1}
            }
        }
    ),
    
    "openai_tts": ServiceCapability(
        service_type="tts",
        service_name="openai",
        display_name="OpenAI TTS",
        description="OpenAI's text-to-speech with natural voices",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", "ar", "hi"],
        supported_models=["tts-1", "tts-1-hd"],
        features=["multiple_voices", "streaming", "high_quality"],
        pricing_tier="paid",
        latency_rating="low",
        quality_rating="good",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "model": {"type": "string", "enum": ["tts-1", "tts-1-hd"], "default": "tts-1"},
                "voice": {"type": "string", "enum": ["alloy", "echo", "fable", "onyx", "nova", "shimmer"], "default": "alloy"},
                "speed": {"type": "number", "minimum": 0.25, "maximum": 4, "default": 1},
                "response_format": {"type": "string", "enum": ["mp3", "opus", "aac", "flac"], "default": "mp3"}
            }
        }
    ),
    
    "cartesia": ServiceCapability(
        service_type="tts",
        service_name="cartesia",
        display_name="Cartesia",
        description="Ultra-low latency neural text-to-speech",
        supported_languages=["en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh"],
        supported_models=["sonic-english", "sonic-multilingual"],
        features=["ultra_low_latency", "emotion_control", "streaming", "real_time"],
        pricing_tier="paid",
        latency_rating="ultra_low",
        quality_rating="excellent",
        requires_api_key=True,
        config_schema={
            "type": "object",
            "properties": {
                "voice_id": {"type": "string", "default": "a0e99841-438c-4a64-b679-ae501e7d6091"},
                "model": {"type": "string", "enum": ["sonic-english", "sonic-multilingual"], "default": "sonic-english"},
                "speed": {"type": "number", "minimum": 0.5, "maximum": 2, "default": 1},
                "emotion": {"type": "string", "enum": ["neutral", "happy", "sad", "angry", "surprised"], "default": "neutral"}
            }
        }
    )
}

# Configuration templates for common use cases
CONFIGURATION_TEMPLATES: List[ServiceConfigurationTemplate] = [
    # STT Templates
    ServiceConfigurationTemplate(
        id="stt_high_accuracy",
        name="High Accuracy Transcription",
        description="Optimized for maximum transcription accuracy",
        service_type="stt",
        service_name="deepgram",
        use_case="business_meetings",
        configuration={
            "model": "nova-2-meeting",
            "language": "en-US",
            "smart_format": True,
            "punctuate": True,
            "profanity_filter": False,
            "interim_results": True,
            "vad_events": True
        },
        tags=["accuracy", "business", "meetings"]
    ),
    
    ServiceConfigurationTemplate(
        id="stt_low_latency",
        name="Low Latency Transcription",
        description="Optimized for real-time conversation",
        service_type="stt",
        service_name="deepgram",
        use_case="real_time_chat",
        configuration={
            "model": "nova-2-conversationalai",
            "language": "en-US",
            "smart_format": False,
            "punctuate": False,
            "interim_results": True,
            "vad_events": False
        },
        tags=["speed", "real_time", "conversation"]
    ),
    
    # LLM Templates
    ServiceConfigurationTemplate(
        id="llm_creative",
        name="Creative Assistant",
        description="High creativity for content generation",
        service_type="llm",
        service_name="openai",
        use_case="content_creation",
        configuration={
            "model": "gpt-4o",
            "temperature": 0.9,
            "max_tokens": 2000,
            "top_p": 0.9,
            "frequency_penalty": 0.5,
            "presence_penalty": 0.3,
            "system_prompt": "You are a creative writing assistant. Be imaginative and engaging in your responses."
        },
        tags=["creative", "content", "writing"]
    ),
    
    ServiceConfigurationTemplate(
        id="llm_analytical",
        name="Analytical Assistant",
        description="Focused on accuracy and analysis",
        service_type="llm",
        service_name="openai",
        use_case="data_analysis",
        configuration={
            "model": "gpt-4o",
            "temperature": 0.1,
            "max_tokens": 1500,
            "top_p": 0.95,
            "frequency_penalty": 0,
            "presence_penalty": 0,
            "system_prompt": "You are an analytical assistant. Provide accurate, fact-based responses with clear reasoning."
        },
        tags=["analytical", "accuracy", "data"]
    ),
    
    # TTS Templates
    ServiceConfigurationTemplate(
        id="tts_natural",
        name="Natural Conversation",
        description="Natural-sounding voice for conversations",
        service_type="tts",
        service_name="elevenlabs",
        use_case="conversation",
        configuration={
            "voice_id": "21m00Tcm4TlvDq8ikWAM",
            "model": "eleven_flash_v2_5",
            "stability": 0.7,
            "similarity_boost": 0.8,
            "style": 0.2,
            "use_speaker_boost": True,
            "speed": 1.0
        },
        tags=["natural", "conversation", "friendly"]
    ),
    
    ServiceConfigurationTemplate(
        id="tts_professional",
        name="Professional Voice",
        description="Clear, professional voice for business",
        service_type="tts",
        service_name="elevenlabs",
        use_case="business",
        configuration={
            "voice_id": "EXAVITQu4vr4xnSDxMaL",
            "model": "eleven_flash_v2_5",
            "stability": 0.8,
            "similarity_boost": 0.9,
            "style": 0.1,
            "use_speaker_boost": True,
            "speed": 0.9
        },
        tags=["professional", "business", "clear"]
    )
]

def get_service_capability(service_type: str, service_name: str) -> ServiceCapability:
    """Get service capability by type and name"""
    key = f"{service_name}" if service_type == "llm" and service_name == "openai" else service_name
    if service_type == "tts" and service_name == "openai":
        key = "openai_tts"
    return SERVICE_CAPABILITIES.get(key)

def get_services_by_type(service_type: str) -> List[ServiceCapability]:
    """Get all services of a specific type"""
    return [cap for cap in SERVICE_CAPABILITIES.values() if cap.service_type == service_type]

def get_configuration_templates(service_type: str = None, service_name: str = None, use_case: str = None) -> List[ServiceConfigurationTemplate]:
    """Get configuration templates with optional filtering"""
    templates = CONFIGURATION_TEMPLATES
    
    if service_type:
        templates = [t for t in templates if t.service_type == service_type]
    if service_name:
        templates = [t for t in templates if t.service_name == service_name]
    if use_case:
        templates = [t for t in templates if t.use_case == use_case]
    
    return templates
